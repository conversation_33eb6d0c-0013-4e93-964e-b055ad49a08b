import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';

interface MenuItem {
  title: string;
  icon: string;
  badge?: string;
  route: keyof RootStackParamList;
  roles?: UserRole[];
  permission?: string; // Optional permission for additional checks
}

type RootStackParamList = {
  Offers: undefined;
  Approvals: undefined;
  Orders: undefined;
  Settings: undefined;
  RegisterWarranty: undefined;
  WarrantyHistory: undefined;
  UserManagement: undefined;
  PricingManagement: undefined;
  SchemeManagement: undefined;
  Leaderboard: undefined;
  Announcements: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const MenuScreen: React.FC = (props) => {
  const { currentUser, hasPermission } = useUser();
  const navigation = useNavigation<NavigationProp>();
  const userRole = currentUser?.role || UserRole.PUBLIC;

  // Define all possible menu items with role restrictions
  const allMenuItems: MenuItem[] = [
    {
      title: 'Special Offers',
      icon: 'local-offer',
      badge: '3',
      route: 'Offers',
      roles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER]
    },
    {
      title: 'My Orders',
      icon: 'shopping-bag',
      badge: '2',
      route: 'Orders',
      roles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER]
    },
    {
      title: 'Warranty Registration',
      icon: 'verified',
      route: 'RegisterWarranty',
      roles: [UserRole.RETAILER]
    },
    {
      title: 'Warranty History',
      icon: 'history',
      route: 'WarrantyHistory',
      roles: [UserRole.RETAILER]
    },
    {
      title: 'Settings',
      icon: 'settings',
      route: 'Settings',
      roles: [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR, UserRole.RETAILER]
    }
  ];

  // Filter menu items based on user role and permissions
  const menuItems = allMenuItems.filter(item => {
    // If roles are specified, check if current role is included
    if (item.roles && !item.roles.includes(userRole)) {
      return false;
    }

    // Additional permission checks for specific items
       if (item.permission) {
      return hasPermission(item.permission);
    }
    return true;
  });

  return (
    <View  className="flex-1">
      {/* User Profile Section */}
      <View className="p-4 bg-primary">
        <View className="flex-row items-center">
          <View className="w-16 h-16 rounded-full bg-white justify-center items-center">
            <Icon name="person" size={32} color="#6366f1" />
          </View>
          <View className="ml-3">
            <Text className="text-white text-lg font-bold">{currentUser?.name || 'Guest User'}</Text>
            <Text className="text-white opacity-80">{currentUser?.email || 'Not logged in'}</Text>
            {currentUser && (
              <View className="bg-white px-2 py-1 rounded-full mt-1">
                <Text className="text-primary text-xs font-medium">
                  {userRole === UserRole.OOGE_TEAM ? 'Ooge Team' :
                   userRole === UserRole.SUPER_STOCKIST ? 'Super Stockist' :
                   userRole === UserRole.DISTRIBUTOR ? 'Distributor' :
                   userRole === UserRole.RETAILER ? 'Retailer' : 'Public'}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Menu Items */}
      <ScrollView className="flex-1 p-4">
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            className="bg-white p-4 rounded-lg shadow mb-3 flex-row items-center justify-between"
            onPress={() => navigation.navigate(item.route)}
          >
            <View className="flex-row items-center">
              <Icon name={item.icon} size={24} color="#6366f1" />
              <Text className="text-lg font-semibold ml-3">{item.title}</Text>
            </View>
            <View className="flex-row items-center">
              {item.badge && (
                <View className="bg-primary rounded-full w-6 h-6 items-center justify-center mr-2">
                  <Text className="text-white text-sm">{item.badge}</Text>
                </View>
              )}
              <Icon name="chevron-right" size={24} color="#6366f1" />
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export default MenuScreen;