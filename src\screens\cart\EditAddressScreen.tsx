import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Switch,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useGetAddressByAddressIdQuery, useUpdateAddressByAddressIdMutation } from './cartApi/apiSlice';
import { useUser } from '../../context/UserContext';
import { Portal, Modal } from 'react-native-paper';
import { states } from '../../utils/common';

const EditAddressScreen = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const addressId = route.params?.addressId;
  const { currentUser } = useUser();

  // Fetch address data by addressId
  const { data: addressData, isLoading: isAddressLoading, error: addressError } = useGetAddressByAddressIdQuery(addressId, { skip: !addressId });
  const [updateAddress, { isLoading }] = useUpdateAddressByAddressIdMutation();

  // Form state
  const [addressType, setAddressType] = useState('HOME');
  const [line1, setLine1] = useState('');
  const [line2, setLine2] = useState('');
  const [landmark, setLandmark] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [pincode, setPincode] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [isPrimaryAddress, setIsPrimaryAddress] = useState(false);
  const [showStateModal, setShowStateModal] = useState(false);

  // Populate form when addressData loads
  useEffect(() => {
    if (addressData) {
      setAddressType(addressData.type || 'HOME');
      setLine1(addressData.line1 || '');
      setLine2(addressData.line2 || '');
      setLandmark(addressData.landmark || '');
      setCity(addressData.city || '');
      setState(addressData.state || '');
      setPincode(addressData.pincode || '');
      setMobileNumber(addressData.mobileNumber || '');
      setIsPrimaryAddress(addressData.isPrimaryAddress === 1);
    }
  }, [addressData]);

  const handleUpdateAddress = async () => {
    if (!line1.trim() || !city.trim() || !state.trim() || !pincode.trim()) {
      Alert.alert('Error', 'Please fill all the required fields');
      return;
    }
    if (!/^\d{6}$/.test(pincode)) {
      Alert.alert('Error', 'Please enter a valid 6-digit pincode');
      return;
    }
    if (!/^\d{10}$/.test(mobileNumber)) {
      Alert.alert('Error', 'Please enter a valid 10-digit mobile number');
      return;
    }

    const payload = {
      userId: currentUser?.id,
      line1,
      line2,
      landmark,
      city,
      country: 'india',
      state,
      type: addressType,
      pincode,
      mobileNumber,
      isPrimaryAddress: isPrimaryAddress ? 1 : 0,
      updatedBy: 1
    };

    try {
      await updateAddress({ addressId, ...payload }).unwrap();
      if (route.params?.onAddressUpdated) {
        route.params.onAddressUpdated();
      }
      Alert.alert('Success', 'Address updated successfully');
      navigation.goBack();
    } catch (error) {
      console.log('Error updating address:', error);
      Alert.alert('Error', `${error?.data?.data} || An error occurred while updating the address.`);
    }
  };

  if (isAddressLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading address...</Text>
      </View>
    );
  }

  if (addressError) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Failed to load address.</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <Icon name="arrow-back" size={24} color="#1f2937" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Address</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
          {/* Address Type Selection */}
          <View style={styles.typeSelectionContainer}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                addressType === 'HOME' && styles.selectedTypeButton
              ]}
              onPress={() => setAddressType('HOME')}
            >
              <Icon
                name="home"
                size={20}
                color={addressType === 'HOME' ? 'white' : '#6b7280'}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  addressType === 'HOME' && styles.selectedTypeButtonText
                ]}
              >
                Home
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                addressType === 'OFFICE' && styles.selectedTypeButton
              ]}
              onPress={() => setAddressType('OFFICE')}
            >
              <Icon
                name="business"
                size={20}
                color={addressType === 'OFFICE' ? 'white' : '#6b7280'}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  addressType === 'OFFICE' && styles.selectedTypeButtonText
                ]}
              >
                Office
              </Text>
            </TouchableOpacity>

            {/* <TouchableOpacity
              style={[
                styles.typeButton,
                addressType === 'OTHER' && styles.selectedTypeButton
              ]}
              onPress={() => setAddressType('OTHER')}
            >
              <Icon
                name="place"
                size={20}
                color={addressType === 'OTHER' ? 'white' : '#6b7280'}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  addressType === 'OTHER' && styles.selectedTypeButtonText
                ]}
              >
                Other
              </Text>
            </TouchableOpacity> */}
          </View>

          {/* Form Fields */}
          <View style={styles.formField}>
            <Text style={styles.label}>Address Line 1 *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={line1}
              onChangeText={setLine1}
              placeholder="Enter your street address"
              placeholderTextColor="#9ca3af"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.label}>Address Line 2</Text>
            <TextInput
              style={styles.input}
              value={line2}
              onChangeText={setLine2}
              placeholder="Apartment, suite, etc. (optional)"
              placeholderTextColor="#9ca3af"
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.label}>Landmark</Text>
            <TextInput
              style={styles.input}
              value={landmark}
              onChangeText={setLandmark}
              placeholder="Nearby landmark (optional)"
              placeholderTextColor="#9ca3af"
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.label}>City *</Text>
            <TextInput
              style={styles.input}
              value={city}
              onChangeText={setCity}
              placeholder="Enter your city"
              placeholderTextColor="#9ca3af"
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.label}>State *</Text>
            <TouchableOpacity
              style={[styles.input, { justifyContent: 'center' }]}
              onPress={() => setShowStateModal(true)}
              activeOpacity={0.7}
            >
              <Text style={{ color: state ? '#1f2937' : '#9ca3af', fontSize: 16 }}>
                {state || 'Select State'}
              </Text>
            </TouchableOpacity>
          </View>

          <Portal>
            <Modal
              visible={showStateModal}
              onDismiss={() => setShowStateModal(false)}
              contentContainerStyle={{
                backgroundColor: '#fff',
                padding: 20,
                marginHorizontal: 20,
                borderRadius: 16,
                maxHeight: 400,
              }}
            >
              <ScrollView>
                {states.map((item) => (
                  <TouchableOpacity
                    key={item.value}
                    style={{ paddingVertical: 12 }}
                    onPress={() => {
                      setState(item.label);
                      setShowStateModal(false);
                    }}
                  >
                    <Text style={{ fontSize: 16, color: '#1f2937' }}>{item.label}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </Modal>
          </Portal>

          <View style={styles.formField}>
            <Text style={styles.label}>Pincode *</Text>
            <TextInput
              style={styles.input}
              value={pincode}
              onChangeText={setPincode}
              placeholder="Enter 6-digit pincode"
              placeholderTextColor="#9ca3af"
              keyboardType="number-pad"
              maxLength={6}
            />
          </View>

          <View style={styles.formField}>
            <Text style={styles.label}>Mobile Number *</Text>
            <TextInput
              style={styles.input}
              value={mobileNumber}
              onChangeText={setMobileNumber}
              placeholder="Enter 10-digit mobile number"
              placeholderTextColor="#9ca3af"
              keyboardType="number-pad"
              maxLength={10}
            />
          </View>

          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Set as default address</Text>
            <Switch
              value={isPrimaryAddress}
              onValueChange={setIsPrimaryAddress}
              trackColor={{ false: '#d1d5db', true: '#c7d2fe' }}
              thumbColor={isPrimaryAddress ? '#6366f1' : '#f4f4f5'}
            />
          </View>

          <TouchableOpacity
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
            onPress={handleUpdateAddress}
            disabled={isLoading}
          >
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Updating...' : 'Update Address'}
            </Text>
          </TouchableOpacity>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  formContainer: {
    padding: 16,
  },
  typeSelectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f9fafb',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    flex: 1,
    marginHorizontal: 4,
  },
  selectedTypeButton: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
    marginLeft: 6,
  },
  selectedTypeButtonText: {
    color: 'white',
  },
  formField: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4b5563',
    marginBottom: 6,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1f2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4b5563',
  },
  saveButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    backgroundColor: '#a5b4fc',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomPadding: {
    height: 40,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#4b5563',
  },
});

export default EditAddressScreen;