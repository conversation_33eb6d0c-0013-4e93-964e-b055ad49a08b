import React, { useEffect, useMemo, useState } from 'react';
import { View, ScrollView, Animated, Alert, ActivityIndicator, Text } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import type { RootState } from '../../redux/store';
import { removeFromCart, updateQuantity, clearCart, updateTotal } from '../../redux/slices/cartSlice';
import { setSelectedAddress } from '../../redux/slices/addressSlice';
// Import components
import CartHeader from './components/CartHeader';
import CartItem from './components/CartItem';
import AddressBook from './components/AddressBook';
import OrderSummary from './components/OrderSummary';
import EmptyCart from './components/EmptyCart';

// Import styles
import { styles } from './styles/cartStyles';
import { useUser } from '../../context/UserContext';
import { 
  useGetCartByUserIdQuery, 
  useDeleteCartItemMutation, 
  useGetAddressesByUserIdQuery, 
  useCreateOrderMutation,
  cartApi // Import the API slice
} from './cartApi/apiSlice';
import { setCartFromServer } from '../../redux/slices/cartSlice';

const CartScreen = () => {
  const navigation = useNavigation<any>();
  const dispatch = useDispatch();
  const { items, subtotal, shippingCost, taxAmount, total, selectedCoupon } = useSelector((state: RootState) => state.cart);
  const { addresses, selectedAddressId } = useSelector((state: RootState) => state.address);
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const { currentUser } = useUser();

  // Fetch cart data from API
  const {
    data: cartData,
    isLoading: isCartLoading,
    error: cartError,
    refetch: refetchCart,
  } = useGetCartByUserIdQuery(Number(currentUser?.id), {
    skip: !currentUser?.id,
  });

  const {
    data: apiAddresses = [],
    isLoading: isAddressesLoading,
    error: addressesError,
    refetch: refetchAddresses,
  } = useGetAddressesByUserIdQuery(Number(currentUser?.id), { skip: !currentUser?.id });

  const [createOrder, { isLoading: isOrderCreating }] = useCreateOrderMutation();
  const [deleteCartItem, { isLoading: isDeleting }] = useDeleteCartItemMutation();

  const mappedAddresses = apiAddresses.map(addr => ({
    id: String(addr.id),
    type: addr.type,
    name: addr.line1,
    street: [addr.line1, addr.line2, addr.landmark].filter(Boolean).join(', '),
    city: addr.city,
    state: addr.state,
    pincode: addr.pincode,
    isDefault: addr.isPrimaryAddress === 1,
  }));

  // State to hold server cart items
  const [serverItems, setServerItems] = useState<any[]>([]);
  const [serverCartTotals, setServerCartTotals] = useState({
    totalAmount: 0,
    totalDiscountPrice: 0,
    totalSellingPrice: 0,
    shippingCharges: 0
  });
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Address error state
  const [addressError, setAddressError] = useState<string>('');

  // CENTRALIZED CART CLEARING FUNCTION
  const clearAllCartState = () => {
    console.log('🗑️ [CART] Clearing ALL cart state');
    dispatch(clearCart());
    setServerItems([]);
    setServerCartTotals({
      totalAmount: 0,
      totalDiscountPrice: 0,
      totalSellingPrice: 0,
      shippingCharges: 0
    });
    dispatch(cartApi.util.invalidateTags(['Cart']));
    console.log('✅ [CART] All cart state cleared');
  };

  const buildOrderPayload = () => {
    const itemsToOrder = serverItems.length > 0 ? serverItems : items;
    return {
      userId: currentUser?.id,
      userAddressId: selectedAddressId,
      items: itemsToOrder.map(item => ({
        productId: item.id,
        variantId: item.variantId,
        quantity: item.quantity,
        price: Number(item.price?.toString().replace(/[^\d.]/g, '')) || 0
      }))
    };
  };

  // Process cart data when it arrives
  useEffect(() => {
    if (cartData) {
      console.log('Processing cart data:', cartData);

      const items = cartData.cartLine?.map(item => ({
        id: item.productId,
        name: item.productName,
        price: `₹${item?.price || 0}`,
        quantity: item.quantity,
        image: '',
        variant: item.variantName,
        cartLineId: item.id,
        variantId: item.variantId
      })) || [];

      setServerItems(items);
      dispatch(setCartFromServer(items));

      setServerCartTotals({
        totalAmount: cartData.totalAmount || 0,
        totalDiscountPrice: cartData.totalDiscountPrice || 0,
        totalSellingPrice: cartData.totalSellingPrice || 0,
        shippingCharges: cartData.shippingCharges || 0
      });
    }
    if (isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [cartData, dispatch, isInitialLoad]);

  // Auto-select primary address when addresses are loaded
  useEffect(() => {
    if (mappedAddresses.length > 0 && !selectedAddressId) {
      const primaryAddress = mappedAddresses.find(addr => addr.isDefault);
      if (primaryAddress) {
        console.log('Auto-selecting primary address:', primaryAddress.id);
        dispatch(setSelectedAddress(primaryAddress.id));
        setAddressError('');
      }
    }
  }, [mappedAddresses, selectedAddressId, dispatch]);

  // Clear address error when address is selected
  useEffect(() => {
    if (selectedAddressId) {
      setAddressError('');
    }
  }, [selectedAddressId]);

  // FIXED: Handle cart errors (including 400 status)
  useEffect(() => {
    console.log('🚨 [CART] Cart error effect triggered:', cartError);
    if (cartError) {
      if ('status' in cartError && (cartError.status === 404 || cartError.status === 400)) {
        const errorMessage = cartError.data?.message?.toLowerCase() || '';
        
        if (errorMessage.includes('cart not found') || 
            errorMessage.includes('cart does not exist')) {
          console.log('🗑️ [CART] Cart not found - server deleted it, clearing ALL state');
          clearAllCartState();
        } else {
          console.log('🚨 [CART] Other 400/404 error:', cartError);
        }
      } else {
        console.log('🚨 [CART] Other cart error:', cartError);
      }
    }
  }, [cartError, dispatch]);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  // FIXED: Simplified handleRemoveItem
  const handleRemoveItem = (id: number, variant?: string, cartLineId?: number) => {
    Alert.alert(
      "Remove Item",
      "Are you sure you want to remove this item?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          onPress: async () => {
            if (cartLineId && currentUser?.id && cartData) {
              try {
                const serverItem = serverItems.find(item => item.cartLineId === cartLineId);
                if (serverItem) {
                  console.log('Deleting cart item with params:', {
                    cartId: cartData.id,
                    productId: id,
                    variantId: serverItem.variantId
                  });

                  await deleteCartItem({
                    cartId: cartData.id,
                    productId: id,
                    variantId: serverItem.variantId
                  }).unwrap();

                  console.log('✅ API deletion successful');
                  
                  // Update local state immediately
                  dispatch(removeFromCart({ id, variant }));
                  
                  // Update server items state
                  const remainingItems = serverItems.filter(item => 
                    item.cartLineId !== cartLineId
                  );
                  setServerItems(remainingItems);
                  
                  // Refetch to get updated cart data
                  // If cart is empty, this will trigger the error effect above
                  refetchCart();
                }
              } catch (error: any) {
                console.error('❌ Error deleting cart item:', error);
                
                // Check if it's a "cart not found" error
                const isCartNotFoundError = (
                  error?.status === 400 || 
                  error?.status === 404
                ) && (
                  error?.data?.message?.toLowerCase().includes('cart not found') ||
                  error?.data?.message?.toLowerCase().includes('cart does not exist')
                );

                if (isCartNotFoundError) {
                  console.log('🗑️ Cart not found during deletion - clearing all state');
                  clearAllCartState();
                } else {
                  // Real error
                  Alert.alert(
                    "Warning",
                    "Item removed from local cart, but server sync failed. The item may reappear when you reload the app.",
                    [
                      {
                        text: "OK",
                        onPress: () => dispatch(removeFromCart({ id, variant }))
                      }
                    ]
                  );
                }
              }
            } else {
              dispatch(removeFromCart({ id, variant }));
            }
          },
        }
      ]
    );
  };

  const handleUpdateQuantity = (id: number, quantity: number, variant?: string, cartLineId?: number) => {
    if (cartLineId && currentUser?.id) {
      dispatch(updateQuantity({ id, quantity, variant }));
      refetchCart();
    } else {
      dispatch(updateQuantity({ id, quantity, variant }));
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      "Clear Cart",
      "Are you sure you want to clear your cart?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          onPress: () => {
            clearAllCartState();
            if (currentUser?.id) {
              refetchCart();
            }
          },
        }
      ]
    );
  };

  const handleBuyNow = async () => {
    if (!selectedAddressId) {
      setAddressError('Please select a delivery address to place your order');
      
      Alert.alert(
        "Address Required", 
        "Please select a delivery address to continue with your order.",
        [
          {
            text: "Add Address",
            onPress: () => navigation.navigate('AddAddress', {
              returnScreen: 'Cart',
              onAddressAdded: () => {
                refetchAddresses();
              }
            })
          },
          {
            text: "Cancel",
            style: "cancel"
          }
        ]
      );
      return;
    }

    const selectedAddress = mappedAddresses.find(addr => addr.id === selectedAddressId);
    if (!selectedAddress) {
      setAddressError('Selected address is no longer available. Please select another address.');
      dispatch(setSelectedAddress(''));
      return;
    }

    setIsLoading(true);

    try {
      const payload = buildOrderPayload();
      console.log('🛒 [CART] Creating order with payload:', payload);

      await createOrder(payload).unwrap();
      console.log('✅ [CART] Order created successfully');

      // Clear all cart state
      clearAllCartState();
      setAddressError('');

      console.log('🎉 [CART] All cart states cleared, navigating to ThankYou');
      navigation.navigate('ThankYou');

    } catch (error: any) {
      console.error('❌ [CART] Order creation failed:', error);
      Alert.alert('Order Failed', error?.data || 'Failed to place order. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectAddress = (address: any) => {
    dispatch(setSelectedAddress(address.id));
    setAddressError('');
  };

  const handleAddNewAddress = () => {
    navigation.navigate('AddAddress', {
      returnScreen: 'Cart',
      onAddressAdded: () => {
        console.log('Address added, refreshing addresses...');
        refetchAddresses();
      }
    });
  };

  const handleEditAddress = (addressId: string) => {
    navigation.navigate('EditAddress', {
      addressId,
      returnScreen: 'Cart',
      onAddressUpdated: () => {
        console.log('Address updated, refreshing addresses...');
        refetchAddresses();
      }
    });
  };

  // FIXED: Better displayItems logic
  const displayItems = useMemo(() => {
    // If we have server items, use them
    if (serverItems.length > 0) {
      return serverItems;
    }
    
    // If server items is empty but we had server data before, 
    // it means cart was cleared on server
    if (serverItems.length === 0 && cartData) {
      return [];
    }
    
    // Otherwise use Redux items (for local-only items)
    return items;
  }, [serverItems, items, cartData]);

  console.log('🔍 [CART SCREEN] Current state:', {
    serverItemsLength: serverItems.length,
    reduxItemsLength: items.length,
    displayItemsLength: displayItems.length,
    cartDataExists: !!cartData,
    cartError: cartError
  });

  if (!currentUser?.id || (isInitialLoad || isCartLoading)) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading your cart...</Text>
      </View>
    );
  }

  if (cartError) {
    console.error('Cart error:', cartError);
  }

  if (!displayItems || displayItems.length === 0) {
    return <EmptyCart fadeAnim={fadeAnim} />;
  }

  return (
    <View style={styles.container}>
      <CartHeader
        itemCount={displayItems.length}
        onClearCart={handleClearCart}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6366f1" />
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <Animated.View style={{ opacity: fadeAnim }}>
            <View style={styles.cartItemsContainer}>
              {displayItems.map((item, index) => (
                <CartItem
                  key={`${item.id}-${item.variant || 'default'}-${item.cartLineId || 'local'}`}
                  item={item}
                  onRemove={(id, variant, cartLineId) => handleRemoveItem(id, variant, cartLineId)}
                  onUpdateQuantity={(id, quantity, variant) =>
                    handleUpdateQuantity(id, quantity, variant, item.cartLineId)
                  }
                  isLast={index === displayItems.length - 1}
                />
              ))}
            </View>

            <AddressBook
              addresses={mappedAddresses}
              selectedAddress={selectedAddressId}
              onSelectAddress={handleSelectAddress}
              onAddNewAddress={handleAddNewAddress}
              onEditAddress={handleEditAddress}
              error={addressError}
              isLoading={isAddressesLoading}
            />

            <OrderSummary
              subtotal={serverItems.length > 0 ? serverCartTotals.totalAmount : subtotal}
              shippingCost={serverItems.length > 0 ? serverCartTotals.shippingCharges : shippingCost}
              discount={serverItems.length > 0 ? serverCartTotals.totalDiscountPrice : 0}
              total={serverItems.length > 0 ? serverCartTotals.totalSellingPrice : total}
              items={displayItems}
              onBuyNow={handleBuyNow}
              isLoading={isLoading || isOrderCreating}
              selectedAddress={selectedAddressId}
              addressError={addressError}
            />
            <View style={styles.bottomPadding} />
          </Animated.View>
        </ScrollView>
      )}
    </View>
  );
};

export default CartScreen;

