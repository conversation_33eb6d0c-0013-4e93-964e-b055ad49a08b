import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Product } from './types';
import { useNavigation } from '@react-navigation/native';

interface ProductListItemProps {
  item: Product;
  onShare: (product: Product) => void;
  onPress?: () => void;
}

const ProductListItem: React.FC<ProductListItemProps> = ({ item, onShare, onPress }) => {
  // const formattedPrice = item.price.replace('₹', '₹ ');
  // const formattedUnitPrice = item.unitPrice.replace('₹', '₹ ');
  const navigation = useNavigation<any>();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      // Use consistent route name - ProductDetail (without 's')
      navigation.navigate('ProductDetail', { product: { id: `prod-${item.id}` } });
    }
  };

  return (
    <TouchableOpacity 
      onPress={handlePress}
      className="bg-white mb-3 mx-2 rounded-lg shadow-sm overflow-hidden"
    >
      <View className="p-3 flex-row">
        <View className="w-[30%] mr-3">
          <Image
            source={{ uri: item.image }}
            className="w-full h-28 rounded-md"
            resizeMode="contain"
          />
        </View>
        
        <View className="flex-1">
          <Text className="text-lg font-bold text-gray-800">{item.name}</Text>
          <Text className="text-gray-600 text-sm mt-1" numberOfLines={2}>{item.description}</Text>
          
          <View className="mt-2 bg-gray-50 p-0 rounded-md">
            <Text className="text-primary font-bold text-lg">
              {/* {formattedPrice} <Text className="text-xs text-gray-500">for lot</Text> */}
            </Text>
            <Text className="text-gray-600 text-xs">
              {/* Unit Price: {formattedUnitPrice} × {item.quantity} units */}
            </Text>
          </View>
          
          <View className="flex-row mt-2 justify-between">
            <TouchableOpacity 
              className="bg-indigo-50 px-3 py-1 rounded-full"
              onPress={handlePress}
            >
              <Text className="text-indigo-600 text-xs font-medium">View Details</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              onPress={() => onShare(item)}
              className="flex-row items-center bg-green-500 px-3 py-1 rounded-full"
            >
              <Icon name="share" size={14} color="white" />
              <Text className="text-white text-xs ml-1">Share on whatsapp</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ProductListItem;
