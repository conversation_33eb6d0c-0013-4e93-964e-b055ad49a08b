import { View, ScrollView, Alert, TouchableOpacity, Text, FlatList, Image, ActivityIndicator, Modal } from 'react-native';
import React, { useState, useEffect } from 'react';
import DateTimePicker from '@react-native-community/datetimepicker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import ApprovalCard from '../../components/Account/ApprovalCard';
import OrderCard from '../../components/Account/OrderCard';
import OrderTabs from '../../components/Account/OrderTabs';
import ReturnsSection from '../../components/Account/ReturnsSection';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import { useCreateConfirmOrderMutation, useGetAllOrdersQuery } from '../cart/cartApi/apiSlice';

const AccountScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const { currentUser, logout } = useUser();
  const userId = currentUser?.id || '';
  const [activeTab, setActiveTab] = useState<string>('Pending');
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [showStartDatePicker, setShowStartDatePicker] = useState<boolean>(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState<boolean>(false);
  const [datePickerDate, setDatePickerDate] = useState<Date>(new Date());
  const [showDateFilter, setShowDateFilter] = useState<boolean>(false);
  const [filteredOrders, setFilteredOrders] = useState<any[]>([]);
  
  // API hooks
  const { data: apiOrdersData, isLoading: ordersLoading, error: ordersError, refetch: refetchOrders } = useGetAllOrdersQuery();
  const [createConfirmOrder, { isLoading: confirmLoading }] = useCreateConfirmOrderMutation();

  console.log('API Orders Data:', apiOrdersData);

  // Helper function to convert role enum to display name
  const getRoleName = (role: UserRole): string => {
    switch (role) {
      case UserRole.OOGE_TEAM: return 'Ooge Team';
      case UserRole.SUPER_STOCKIST: return 'Super Stockist';
      case UserRole.DISTRIBUTOR: return 'Distributor';
      case UserRole.RETAILER: return 'Retailer';
      default: return 'Public';
    }
  };

  // Get user profile information
  const userProfile = {
    name: currentUser?.name || 'Guest User',
    type: currentUser?.role ? getRoleName(currentUser.role) : 'Public',
    memberSince: '2023',
    creditLimit: '₹5,00,000',
    activeOrders: 4,
    totalOrders: 125
  };

  // Transform API orders data
  const transformApiOrders = (apiData: any[]) => {
    if (!Array.isArray(apiData)) {
      console.log('API data is not an array:', apiData);
      return [];
    }

    console.log('Transforming API orders:', apiData.length, 'orders');
    
    return apiData.map((order: any) => {
      const itemsDescription = order.items?.map((item: any) => 
        `${item.quantity}X Product ${item.productId} (₹${item.price})`
      ) || [];

      const transformedOrder = {
        id: order.id,
        items: itemsDescription,
        status: order.orderStatus || 'PENDING',
        date: formatOrderDate(order.orderDate),
        total: `₹${order.totalAmount?.toLocaleString() || '0'}`,
        userId: order.userId,
        rawItems: order.items || [],
        rawOrder: order // Keep original order data for debugging
      };

      console.log('Transformed order:', transformedOrder.id, 'Status:', transformedOrder.status, 'Items:', transformedOrder.items.length);
      return transformedOrder;
    });
  };

  // Format order date
  const formatOrderDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Date formatting error:', error);
      return dateString;
    }
  };

  // Load orders on mount and when API data changes
  useEffect(() => {
    console.log('useEffect triggered - ordersLoading:', ordersLoading, 'apiOrdersData:', !!apiOrdersData, 'ordersError:', !!ordersError);
    
    if (apiOrdersData && Array.isArray(apiOrdersData)) {
      console.log('Loading orders from API...', apiOrdersData.length, 'orders found');
      const transformedOrders = transformApiOrders(apiOrdersData);
      console.log('Transformed orders:', transformedOrders.length);
      setOrders(transformedOrders);
      setLoading(false);
    } else if (ordersError) {
      console.error('Orders API Error:', ordersError);
      setLoading(false);
    } else if (!ordersLoading) {
      console.log('No loading, no data, no error - setting loading to false');
      setLoading(false);
    }
  }, [apiOrdersData, ordersError, ordersLoading]);

  // Filter orders when tab changes or date filters change
  useEffect(() => {
    console.log('Filter effect triggered - orders:', orders.length, 'activeTab:', activeTab);
    if (orders.length > 0) {
      filterOrders();
    } else {
      setFilteredOrders([]);
    }
  }, [activeTab, startDate, endDate, orders]);

  // Filter orders based on status and date range
  const filterOrders = () => {
    console.log('Filtering orders for tab:', activeTab, 'Total orders:', orders.length);
    
    let statusFilter = activeTab.toUpperCase();
    
    let filtered = orders.filter(order => {
      const matches = order.status === statusFilter;
      console.log(`Order ${order.id} status: ${order.status}, filter: ${statusFilter}, matches: ${matches}`);
      return matches;
    });

    console.log(`Filtered ${filtered.length} orders for status: ${statusFilter}`);

    // Apply date filters if they exist
    if (startDate && endDate) {
      const beforeDateFilter = filtered.length;
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.date);
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        return orderDate >= start && orderDate <= end;
      });
      console.log(`Date filter applied: ${beforeDateFilter} -> ${filtered.length} orders`);
    } else if (startDate) {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.date);
        const start = new Date(startDate);
        return orderDate >= start;
      });
    } else if (endDate) {
      filtered = filtered.filter(order => {
        const orderDate = new Date(order.date);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);
        return orderDate <= end;
      });
    }

    console.log(`Final filtered orders: ${filtered.length}`);
    setFilteredOrders(filtered);
  };

  // Handle order confirmation/rejection
  const handleOrderAction = async (orderId: number, action: 'CONFIRMED' | 'REJECTED') => {
    try {
      console.log(`Attempting to ${action.toLowerCase()} order ${orderId}`);
      
      const result = await createConfirmOrder({
        orderId: orderId,
        newStatus: action
      }).unwrap();

      console.log('Order action result:', result);

      if (result.message === 'SUCCESS') {
        Alert.alert(
          'Success',
          `Order has been ${action.toLowerCase()} successfully.`,
          [
            {
              text: 'OK',
              onPress: () => {
                console.log('Refetching orders after successful action');
                refetchOrders();
              }
            }
          ]
        );
      } else {
        throw new Error(result.data || 'Unknown error occurred');
      }
    } catch (error: any) {
      console.error('Order action error:', error);
      
      let errorMessage = 'Failed to update order status';
      if (error.data?.message === 'FAILURE') {
        errorMessage = error.data.data || 'You are not authorized to confirm or reject orders.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    }
  };

  // Clear date filters
  const clearDateFilters = () => {
    setStartDate('');
    setEndDate('');
    setShowDateFilter(false);
  };

  // Apply predefined date range
  const applyDateRange = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);

    setEndDate(formatDateString(end));
    setStartDate(formatDateString(start));
    setShowDateFilter(false);
  };

  // Format date to YYYY-MM-DD string
  const formatDateString = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle date picker change
  const onDateChange = (_event: any, selectedDate?: Date) => {
    if (selectedDate) {
      if (showStartDatePicker) {
        setStartDate(formatDateString(selectedDate));
        setShowStartDatePicker(false);
      } else if (showEndDatePicker) {
        setEndDate(formatDateString(selectedDate));
        setShowEndDatePicker(false);
      }
    } else {
      setShowStartDatePicker(false);
      setShowEndDatePicker(false);
    }
  };

  // Debug logging
  console.log('Current state:', {
    ordersLoading,
    ordersCount: orders.length,
    filteredOrdersCount: filteredOrders.length,
    activeTab,
    currentUser: !!currentUser
  });

  // Show loading indicator while fetching data
  if (ordersLoading || (loading && currentUser)) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50">
        <ActivityIndicator size="large" color="#6366f1" />
        <Text className="mt-4 text-gray-500">Loading account information...</Text>
      </View>
    );
  }

  // Show login prompt for public users
  if (!currentUser || currentUser.role === UserRole.PUBLIC) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50 p-6">
        <Icon name="account-circle" size={80} color="#d1d5db" />
        <Text className="text-xl font-bold text-gray-800 mt-4 mb-2">Not Logged In</Text>
        <Text className="text-gray-500 text-center mb-8">
          Please log in to view your account information and manage your orders.
        </Text>
        <TouchableOpacity
          className="bg-indigo-600 py-3 px-6 rounded-lg w-full items-center"
          onPress={() => navigation.navigate('Login')}
        >
          <Text className="text-white font-bold text-lg">Log In</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Profile Header */}
      <View className="bg-indigo-600 pt-6 pb-8">
        <View className="px-4">
          <View className="flex-row items-center justify-between mb-4">
            <View className="flex-row items-center">
              <View className="w-16 h-16 bg-white rounded-full items-center justify-center">
                <Text className="text-2xl font-bold text-indigo-600">
                  {userProfile.name.charAt(0)}
                </Text>
              </View>
              <View className="ml-4">
                <Text className="text-white text-xl font-bold">{userProfile.name}</Text>
                <View className="flex-row items-center mt-1">
                  <View className="bg-indigo-400 rounded-full px-2 py-0.5 mr-2">
                    <Text className="text-white text-xs font-medium">{userProfile.type}</Text>
                  </View>
                  <Text className="text-indigo-200">Since {userProfile.memberSince}</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
     
     {/* Orders Section */}      
     <View className="mx-4 mb-6">
        <Text className="text-lg font-bold text-gray-800 mb-4">Recent Orders</Text>
        <View className="bg-white rounded-xl shadow-sm overflow-hidden">
          <OrderTabs activeTab={activeTab} setActiveTab={setActiveTab} />

          {/* Date Filter Button */}
          <View className="flex-row justify-between items-center px-4 py-2 border-b border-gray-100">
            <Text className="text-gray-700 font-medium">Filter by date</Text>
            <TouchableOpacity
              className="bg-indigo-50 px-3 py-1 rounded-lg flex-row items-center"
              onPress={() => setShowDateFilter(true)}
            >
              <Icon name="date-range" size={18} color="#6366f1" />
              <Text className="text-indigo-600 ml-1">
                {startDate || endDate ? 'Edit Filter' : 'Add Filter'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Active Filter Display */}
          {(startDate || endDate) && (
            <View className="flex-row justify-between items-center px-4 py-2 bg-indigo-50">
              <Text className="text-indigo-700">
                {startDate && endDate
                  ? `From ${startDate} to ${endDate}`
                  : startDate
                    ? `From ${startDate}`
                    : `Until ${endDate}`
                }
              </Text>
              <TouchableOpacity onPress={clearDateFilters}>
                <Icon name="close" size={18} color="#6366f1" />
              </TouchableOpacity>
            </View>
          )}

          <FlatList
            data={filteredOrders}
            renderItem={({ item: order }) => (
              <OrderCard
                key={order.id}
                orderNumber={order.id}
                items={order.items}
                date={order.date}
                total={order.total}
                status={order.status}
                onCallBuyer={() => Alert.alert('Calling', `Calling buyer for order #${order.id}`)}
                onMarkShipped={() => Alert.alert('Shipping', `Order #${order.id} marked as shipped`)}
              />
            )}
            keyExtractor={order => order.id.toString()}
            contentContainerStyle={{ padding: 16 }}
            nestedScrollEnabled={true}
            scrollEnabled={true}
            showsVerticalScrollIndicator={true}
            ListEmptyComponent={
              <View className="py-8 items-center">
                <Icon name="receipt-long" size={48} color="#e5e7eb" />
                <Text className="text-gray-500 mt-2 text-center">
                  {startDate || endDate
                    ? 'No orders found for the selected date range'
                    : `No ${activeTab.toLowerCase()} orders found`}
                </Text>
                <Text className="text-gray-400 text-sm mt-1 text-center">
                  Total orders available: {orders.length}
                </Text>
              </View>
            }
          />
        </View>
      </View>

      {/* Pending Orders Section - Only show for admin, super stockist, and distributor */}
      {currentUser && [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role) && (
        <View className="mx-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">
            Pending Orders Management ({orders.filter(order => order.status === 'PENDING').length})
          </Text>
          <View className="bg-white rounded-xl shadow-sm overflow-hidden">
            {ordersLoading ? (
              <View className="p-8 items-center">
                <ActivityIndicator size="large" color="#6366f1" />
                <Text className="text-gray-500 mt-2">Loading pending orders...</Text>
              </View>
            ) : (
              <FlatList
                data={orders.filter(order => order.status === 'PENDING')}
                renderItem={({ item: order }) => (
                  <View className="p-4 border-b border-gray-100">
                    <View className="flex-row justify-between items-start mb-3">
                      <View className="flex-1">
                        <Text className="text-lg font-bold text-gray-800 mb-1">
                          Order #{order.id}
                        </Text>
                        <Text className="text-gray-600 text-sm mb-2">
                          User ID: {order.userId}
                        </Text>
                        <Text className="text-gray-600 text-sm mb-2">
                          Date: {order.date}
                        </Text>
                        <Text className="text-lg font-semibold text-green-600 mb-2">
                          {order.total}
                        </Text>
                        
                        {/* Order Items */}
                        <View className="mb-3">
                          <Text className="text-gray-700 font-medium mb-1">Items ({order.rawItems.length}):</Text>
                          {order.rawItems.map((item: any, index: number) => (
                            <Text key={index} className="text-gray-600 text-sm ml-2">
                              • Product {item.productId} (Variant {item.variantId}): {item.quantity} × ₹{item.price}
                            </Text>
                          ))}
                        </View>
                      </View>
                      
                      <View className="bg-yellow-50 px-3 py-1 rounded-full">
                        <Text className="text-yellow-600 text-xs font-semibold">
                          {order.status}
                        </Text>
                      </View>
                    </View>
                    
                    {/* Action Buttons */}
                    <View className="flex-row space-x-3">
                      <TouchableOpacity
                        className="flex-1 bg-green-600 py-3 rounded-lg items-center"
                        onPress={() => {
                          Alert.alert(
                            'Confirm Order',
                            `Are you sure you want to confirm order #${order.id}?`,
                            [
                              {
                                text: 'Cancel',
                                style: 'cancel',
                              },
                              {
                                text: 'Confirm',
                                onPress: () => handleOrderAction(order.id, 'CONFIRMED'),
                              },
                            ]
                          );
                        }}
                        disabled={confirmLoading}
                      >
                        {confirmLoading ? (
                          <ActivityIndicator size="small" color="white" />
                        ) : (
                          <View className="flex-row items-center">
                            <Icon name="check-circle" size={20} color="white" />
                            <Text className="text-white font-semibold ml-1">Confirm</Text>
                          </View>
                        )}
                      </TouchableOpacity>
                      
                      <TouchableOpacity
                        className="flex-1 bg-red-600 py-3 rounded-lg items-center"
                        onPress={() => {
                          Alert.alert(
                            'Reject Order',
                            `Are you sure you want to reject order #${order.id}?`,
                            [
                              {
                                text: 'Cancel',
                                style: 'cancel',
                              },
                              {
                                text: 'Reject',
                                style: 'destructive',
                                onPress: () => handleOrderAction(order.id, 'REJECTED'),
                              },
                            ]
                          );
                        }}
                        disabled={confirmLoading}
                      >
                        {confirmLoading ? (
                          <ActivityIndicator size="small" color="white" />
                        ) : (
                          <View className="flex-row items-center">
                            <Icon name="cancel" size={20} color="white" />
                            <Text className="text-white font-semibold ml-1">Reject</Text>
                          </View>
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
                keyExtractor={order => `pending-${order.id}`}
                nestedScrollEnabled={true}
                scrollEnabled={true}
                showsVerticalScrollIndicator={true}
                ListEmptyComponent={
                  <View className="py-8 items-center">
                    <Icon name="assignment-turned-in" size={48} color="#e5e7eb" />
                    <Text className="text-gray-500 mt-2 text-center">
                      No pending orders to review
                    </Text>
                    <Text className="text-gray-400 text-sm mt-1 text-center">
                      All orders have been processed
                    </Text>
                  </View>
                }
              />
            )}
          </View>
        </View>
      )}


      {/* Returns & Warranty Section - Only show for retailers */}
      {currentUser && currentUser.role === UserRole.RETAILER && (
        <View className="mx-4 mb-6">
          <Text className="text-lg font-bold text-gray-800 mb-4">Service & Support</Text>
          <ReturnsSection />
        </View>
      )}

      {/* Date Filter Modal */}
      <Modal
        visible={showDateFilter}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDateFilter(false)}
      >
        <View className="flex-1 justify-center items-center bg-black bg-opacity-50">
          <View className="bg-white rounded-xl p-5 w-11/12 max-w-md">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-bold text-gray-800">Filter Orders by Date</Text>
              <TouchableOpacity onPress={() => setShowDateFilter(false)}>
                <Icon name="close" size={24} color="#6b7280" />
              </TouchableOpacity>
            </View>

            {/* Predefined Date Ranges */}
            <Text className="text-gray-700 font-medium mb-2">Quick Filters</Text>
            <View className="flex-row flex-wrap mb-4">
              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mr-2 mb-2"
                onPress={() => applyDateRange(7)}
              >
                <Text className="text-indigo-600">Last 7 days</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mr-2 mb-2"
                onPress={() => applyDateRange(30)}
              >
                <Text className="text-indigo-600">Last 30 days</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mr-2 mb-2"
                onPress={() => applyDateRange(90)}
              >
                <Text className="text-indigo-600">Last 90 days</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-50 px-3 py-2 rounded-lg mb-2"
                onPress={() => {
                  const now = new Date();
                  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                  setStartDate(formatDateString(startOfMonth));
                  setEndDate(formatDateString(now));
                }}
              >
                <Text className="text-indigo-600">This Month</Text>
              </TouchableOpacity>
            </View>

            <Text className="text-gray-700 font-medium mb-2">Custom Date Range</Text>

            {/* Start Date Picker */}
            <Text className="text-gray-700 mb-2">Start Date</Text>
            <TouchableOpacity
              className="border border-gray-300 rounded-lg p-3 mb-4 flex-row justify-between items-center"
              onPress={() => {
                setShowStartDatePicker(true);
                setShowEndDatePicker(false);
                setDatePickerDate(startDate ? new Date(startDate) : new Date());
              }}
            >
              <Text>{startDate || 'Select start date'}</Text>
              <Icon name="calendar-today" size={20} color="#6366f1" />
            </TouchableOpacity>

            {/* End Date Picker */}
            <Text className="text-gray-700 mb-2">End Date</Text>
            <TouchableOpacity
              className="border border-gray-300 rounded-lg p-3 mb-4 flex-row justify-between items-center"
              onPress={() => {
                setShowEndDatePicker(true);
                setShowStartDatePicker(false);
                setDatePickerDate(endDate ? new Date(endDate) : new Date());
              }}
            >
              <Text>{endDate || 'Select end date'}</Text>
              <Icon name="calendar-today" size={20} color="#6366f1" />
            </TouchableOpacity>

            {/* Date Picker */}
            {(showStartDatePicker || showEndDatePicker) && (
              <DateTimePicker
                value={datePickerDate}
                mode="date"
                display="default"
                onChange={onDateChange}
              />
            )}

            <View className="flex-row justify-between mt-2">
              <TouchableOpacity
                className="bg-gray-200 px-4 py-2 rounded-lg"
                onPress={clearDateFilters}
              >
                <Text className="text-gray-700 font-medium">Clear</Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="bg-indigo-600 px-4 py-2 rounded-lg"
                onPress={() => {
                  // Validate dates before applying filter
                  let hasError = false;

                  if (startDate && endDate) {
                    const start = new Date(startDate);
                    const end = new Date(endDate);

                    if (start > end) {
                      Alert.alert('Invalid Date Range', 'Start date cannot be after end date');
                      hasError = true;
                    }
                  }

                  if (!hasError) {
                    setShowDateFilter(false);
                  }
                }}
              >
                <Text className="text-white font-medium">Apply Filter</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

export default AccountScreen;