import axios, { AxiosInstance } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createNavigationContainerRef } from '@react-navigation/native';
import { CommonActions } from '@react-navigation/native';
import { User, UserRole } from '../../data/mockData';
import { BACKEND_URL, LOGIN_URL } from '../../utils/constants';
import {jwtDecode} from 'jwt-decode';

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
}

interface DecodedToken {
  sub: string;
  exp: number;
  iat: number;
  roles: string[];
  // Add other fields that exist in your token
}

export const navigationRef = createNavigationContainerRef();

const apiService: AxiosInstance = axios.create({
  baseURL: BACKEND_URL,
  headers: {
    'Content-Type': 'application/json',
    'deviceType': 'APP'
  }
});

// Request interceptor for adding auth token
apiService.interceptors.request.use(
  async (config) => {
    const auth = await AsyncStorage.getItem('auth');
    if (auth) {
      const { accessToken } = JSON.parse(auth);
      if (accessToken) {
        // Decode token to check expiration
        const decodedToken = jwtDecode<DecodedToken>(accessToken);
        console.log('Decoded token:', decodedToken);
        if (decodedToken.exp) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        } else {
          // Token expired, logout user
          await logout();
        }
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling 401 errors
apiService.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response && error.response.status === 401) {
      await AsyncStorage.multiRemove(['auth', 'user']);
      if (navigationRef.isReady()) {
        navigationRef.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: 'Login' }]
          })
        );
      }
    }
    return Promise.reject(error);
  }
);


const authenticate = async (username: string, password: string): Promise<User | null> => {
  try {
    const response = await apiService.post<AuthResponse>(`${LOGIN_URL}`, {
      username,
      password,
      source: 'login'
    });

    if (response.data?.accessToken) {
      const authData = {
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
        tokenType: response.data.tokenType
      };

      await AsyncStorage.setItem('auth', JSON.stringify(authData));

      const decoded: any = jwtDecode(response.data.accessToken);
      console.log('Decoded token:', decoded);

      const user: User = {
        id: decoded.id,
        name: decoded.firstName + ' ' + decoded.lastName,
        email: decoded.email,
        phone: decoded.mobileNumber,
        role: decoded.role, // <-- Save role from token
        permissions: decoded.permissions, // <-- Save permissions from token
        status: 'active',
        createdAt: new Date().toISOString().split('T')[0],
        apiData: response.data
      };

      await AsyncStorage.setItem('user', JSON.stringify(user));
      return user;
    }
    return null;
  } catch (error) {
    console.error('Authentication error:', error);
    throw error;
  }
};

const logout = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove(['auth', 'user']);
    if (navigationRef.isReady()) {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Login' }]
        })
      );
    }
  } catch (error) {
    console.error('Logout error:', error);
    throw error;
  }
};

const getDecodedToken = async (): Promise<any | null> => {
  try {
    const auth = await AsyncStorage.getItem('auth');
    if (auth) {
      const { accessToken } = JSON.parse(auth);
      if (accessToken) {
        return jwtDecode(accessToken);
      }
    }
    return null;
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

// API methods using the interceptor-configured instance
const api = {
  get: <T>(endpoint: string, params?: object): Promise<T> => {
    return apiService.get(endpoint, { params }).then(response => response.data);
  },

  post: <T>(endpoint: string, data: any): Promise<T> => {
    return apiService.post(endpoint, data).then(response => response.data);
  },

  put: <T>(endpoint: string, data: any): Promise<T> => {
    return apiService.put(endpoint, data).then(response => response.data);
  },

  delete: <T>(endpoint: string): Promise<T> => {
    return apiService.delete(endpoint).then(response => response.data);
  }
};

export default {
  authenticate,
  logout,
  getDecodedToken,
  ...api
};