import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { UserRole } from '../../data/mockData';
import { useUser } from '../../context/UserContext';
import { useGetAllOrdersByUserIdQuery } from '../cart/cartApi/apiSlice';

const statusColors: Record<string, string> = {
  pending: '#facc15',      // yellow-400
  approved: '#3b82f6',     // blue-500
  shipped: '#6366f1',      // indigo-500
  delivered: '#22c55e',    // green-500
  confirmed: '#1e40af',    // blue-700
  cancelled: '#ef4444',    // red-500
  default: '#6b7280',      // gray-500
};

const OrdersScreen = () => {
  const navigation = useNavigation();
  const { currentUser } = useUser();

  

  // Fetch orders from API
  const { data: orders = [], isLoading, isError } = useGetAllOrdersByUserIdQuery(currentUser?.id, {
    skip: !currentUser?.id,
  });

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#6366f1" />
        <Text style={styles.loadingText}>Loading orders...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load orders.</Text>
      </View>
    );
  }

  if (!orders.length) {
    return (
      <View style={styles.centered}>
        <Text style={styles.emptyText}>No orders found.</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {orders.slice().reverse().map(order => (
        <TouchableOpacity
          key={order.id}
          style={styles.card}
          activeOpacity={0.8}
        >
          <View style={styles.headerRow}>
            <Text style={styles.orderNumber}>Order #{order.id}</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: statusColors[order.orderStatus?.toLowerCase()] || statusColors.default }
            ]}>
              <Text style={styles.statusText}>
                {order.orderStatus?.charAt(0).toUpperCase() + order.orderStatus?.slice(1).toLowerCase()}
              </Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.label}>Date:</Text>
            <Text style={styles.value}>
              {order.orderDate ? new Date(order.orderDate).toLocaleString() : '-'}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.label}>Total:</Text>
            <Text style={styles.value}>₹{order.totalAmount ?? 0}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.label}>Items:</Text>
            <Text style={styles.value}>{Array.isArray(order.items) ? order.items.length : 0}</Text>
          </View>
          {/* Show product/variant/qty summary */}
          <View style={styles.itemsList}>
            {Array.isArray(order.items) && order.items.map((item, idx) => (
              <Text key={idx} style={styles.itemText}>
                • Product {item.productId} (Variant {item.variantId}) x{item.quantity}
              </Text>
            ))}
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    paddingVertical: 8,
  },
  card: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  statusBadge: {
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  statusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 13,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  label: {
    color: '#64748b',
    fontSize: 14,
  },
  value: {
    color: '#334155',
    fontSize: 14,
    fontWeight: '500',
  },
  itemsList: {
    marginTop: 8,
  },
  itemText: {
    color: '#475569',
    fontSize: 13,
    marginLeft: 4,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    color: '#6366f1',
    fontSize: 16,
  },
  errorText: {
    color: '#ef4444',
    fontSize: 16,
  },
  emptyText: {
    color: '#64748b',
    fontSize: 16,
  },
});

export default OrdersScreen;