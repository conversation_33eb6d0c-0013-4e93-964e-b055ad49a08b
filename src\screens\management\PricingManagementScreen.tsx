import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Animated,
  Alert,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Text,
  Searchbar,
  Button,
  useTheme,
  TextInput,
  Surface,
  IconButton,
  Chip,
} from 'react-native-paper';
import { useUser } from '../../context/UserContext';
import { UserRole, products, User } from '../../data/mockData';
import PricingCard from '../../components/management/PricingCard';
import EnhancedPricingCard from '../../components/management/EnhancedPricingCard';
import FilterChips from '../../components/management/FilterChips';
import BaseManagementScreen from '../../components/management/BaseManagementScreen';
import EmptyState from '../../components/common/EmptyState';
import {
  useGetCategoriesQuery,
  useGetProductsQuery,
  useGetProductsByCategoryQuery,
} from '../../services/api/apiSlice';
import {
  useCreateDistributorPriceMutation,
  useCreateRetailerPriceMutation,
  DistributorPriceRequest,
  RetailerPriceRequest,
} from './api/price';

type RootStackParamList = {
  PricingManagement: { userId?: string; userName?: string; userRole?: UserRole; selectedUsers?: User[] };
  UserManagement: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'PricingManagement'>;
type PricingManagementRouteProp = RouteProp<RootStackParamList, 'PricingManagement'>;

interface ProductVariant {
  id: number;
  unitOfMeasurement: string;
  quantity: number;
  catalogType: string;
  urls?: string[];
  price?: number;
  sellingPrice?: number;
}

interface PricingItem {
  productId: string;
  productName: string;
  productCode?: string;
  description?: string;
  basePrice: number;
  margin: number;
  finalPrice: number;
  isCustomPrice: boolean;
  category: string;
  variants?: ProductVariant[];
  selectedVariantId?: string;
  targetUserId?: string;
}

const PricingManagementScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<PricingManagementRouteProp>();
  const { currentUser } = useUser();
  const theme = useTheme();

  // State declarations
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    error: categoriesError,
    refetch: refetchCategories
  } = useGetCategoriesQuery({ page: 0, count: 50, type: 1, status: 1 });

  const {
    data: productsData,
    isLoading: isProductsLoading,
    error: productsError,
    refetch: refetchProducts
  } = useGetProductsQuery({ page: 0, count: 100, type: 2, status: 1 }, {
    skip: selectedCategory !== null && selectedCategory !== 'All' // Skip when a specific category is selected
  });

  // Get products by category when a specific category is selected
  const {
    data: categoryProductsData,
    isLoading: isCategoryProductsLoading,
    error: categoryProductsError,
    refetch: refetchCategoryProducts
  } = useGetProductsByCategoryQuery(
    categoriesData?.find((cat: any) => cat.name === selectedCategory)?.id,
    {
      skip: !selectedCategory || selectedCategory === 'All' || !categoriesData
    }
  );




  // Extract route params
  const { userName, userRole, selectedUsers } = route.params || {};

  // Determine if this is bulk mode
  const isBulkMode = selectedUsers && selectedUsers.length > 0;

  // Set display name based on mode
  const displayName = isBulkMode
    ? `${selectedUsers.length} Users Selected`
    : userName || 'All Users';

  // const effectiveUserRole = userRole || (currentUser ? currentUser.role : UserRole.OOGE_TEAM);

  // Determine child role based on current user's role if not specified in route params
  const getChildRoleFromUserRole = (role: UserRole): UserRole => {
    switch (role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return UserRole.RETAILER;
    }
  };

  // If userRole is not specified in route params, determine it based on current user's role
  const effectiveUserRole = route.params?.userRole || getChildRoleFromUserRole(currentUser?.role || UserRole.OOGE_TEAM);

  // Tab navigation state
  const [activeTab, setActiveTab] = useState<'individual' | 'global'>('individual');
  const tabPosition = useState(new Animated.Value(0))[0];

  const [isLoading, setIsLoading] = useState(true);
  const [pricingItems, setPricingItems] = useState<PricingItem[]>([]);
  const [globalPricingItems, setGlobalPricingItems] = useState<PricingItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [globalMargin, setGlobalMargin] = useState('');
  const [applyingGlobalMargin, setApplyingGlobalMargin] = useState(false);
  const [globalMarkup, setGlobalMarkup] = useState('');
  const [productVariants, setProductVariants] = useState<{[key: string]: ProductVariant[]}>({});
  const [selectedVariants, setSelectedVariants] = useState<{[key: string]: string}>({});

  // Pricing API hooks
  const [createDistributorPrice, { isLoading: isCreatingDistributorPrice }] = useCreateDistributorPriceMutation();
  const [createRetailerPrice, { isLoading: isCreatingRetailerPrice }] = useCreateRetailerPriceMutation();

  // Get the applicable child role based on current user's role
  const getApplicableChildRole = (): UserRole | null => {
    if (!currentUser) return null;

    switch (currentUser.role) {
      case UserRole.OOGE_TEAM:
        return UserRole.SUPER_STOCKIST;
      case UserRole.SUPER_STOCKIST:
        return UserRole.DISTRIBUTOR;
      case UserRole.DISTRIBUTOR:
        return UserRole.RETAILER;
      default:
        return null;
    }
  };

  // Get the margin field name based on role
  const getMarginFieldName = (role: UserRole): string | null => {
    switch (role) {
      case UserRole.SUPER_STOCKIST:
        return 'superStockist';
      case UserRole.DISTRIBUTOR:
        return 'distributor';
      case UserRole.RETAILER:
        return 'retailer';
      default:
        return null;
    }
  };

  // Switch between tabs
  const switchTab = (tab: 'individual' | 'global') => {
    Animated.spring(tabPosition, {
      toValue: tab === 'individual' ? 0 : 1,
      useNativeDriver: false,
      friction: 8,
      tension: 70
    }).start();
    setActiveTab(tab);
  };

  // Extract unique categories from products
  useEffect(() => {
    const isLoadingData = isCategoriesLoading || isProductsLoading ||
      (!!selectedCategory && selectedCategory !== 'All' && isCategoryProductsLoading);
    setIsLoading(isLoadingData);
  }, [isCategoriesLoading, isProductsLoading, isCategoryProductsLoading, selectedCategory]);

  useEffect(() => {
    if (categoriesData) {
      // Extract unique category names from the API response
      const uniqueCategories = Array.from(
        new Set(categoriesData.map((cat: any) => cat.name))
      ).filter(Boolean) as string[];

      setCategories(uniqueCategories);
    }
  }, [categoriesData]);

  // Load pricing data
  // useEffect(() => {
  //   // Simulate API call with a delay
  //   setTimeout(() => {
  //     // Determine which child role's pricing we're managing
  //     const childRole = effectiveUserRole || getApplicableChildRole();
  //     if (!childRole) {
  //       setIsLoading(false);
  //       return;
  //     }

  //     const marginField = getMarginFieldName(childRole);
  //     if (!marginField) {
  //       setIsLoading(false);
  //       return;
  //     }

  //     // Generate individual pricing items based on products
  //     const individualItems: PricingItem[] = products.map(product => {
  //       // Get the appropriate margin based on the child role we're setting prices for
  //       let margin = 0;
  //       if (marginField && product.margins) {
  //         if (marginField === 'superStockist') margin = product.margins.superStockist || 10;
  //         else if (marginField === 'distributor') margin = product.margins.distributor || 15;
  //         else if (marginField === 'retailer') margin = product.margins.retailer || 20;
  //       } else {
  //         margin = childRole === UserRole.SUPER_STOCKIST ? 10 :
  //                 childRole === UserRole.DISTRIBUTOR ? 15 : 20;
  //       }

  //       // Calculate the base price based on the parent's cost
  //       const basePrice = product.basePrice;

  //       // Calculate the final price with margin
  //       const finalPrice = basePrice * (1 + margin / 100);

  //       return {
  //         productId: product.id,
  //         productName: product.name,
  //         basePrice: basePrice,
  //         margin,
  //         finalPrice,
  //         isCustomPrice: false,
  //         category: product.category || 'Uncategorized'
  //       };
  //     });

  //     // Generate global pricing items (similar structure but for all users of this role)
  //     const globalItems: PricingItem[] = products.map(product => {
  //       // For global pricing, we use standard margins based on role
  //       const standardMargin =
  //         childRole === UserRole.SUPER_STOCKIST ? 10 :
  //         childRole === UserRole.DISTRIBUTOR ? 15 : 20;

  //       const basePrice = product.basePrice;
  //       const finalPrice = basePrice * (1 + standardMargin / 100);

  //       return {
  //         productId: product.id,
  //         productName: product.name,
  //         basePrice: basePrice,
  //         margin: standardMargin,
  //         finalPrice,
  //         isCustomPrice: false,
  //         category: product.category || 'Uncategorized'
  //       };
  //     });

  //     setPricingItems(individualItems);
  //     setGlobalPricingItems(globalItems);
  //     setIsLoading(false);
  //   }, 1000);
  // }, [userRole, effectiveUserRole, currentUser]);

  // Combined effect to handle both all products and category-specific products
  useEffect(() => {
    // Determine which data source to use
    const currentProductsData = selectedCategory && selectedCategory !== 'All'
      ? categoryProductsData
      : productsData;

    if (!currentProductsData) return;

    console.log('📊 [PRICING MANAGEMENT] Loading products:', {
      selectedCategory,
      dataSource: selectedCategory && selectedCategory !== 'All' ? 'category' : 'all',
      productCount: currentProductsData.length
    });

    // Determine which child role's pricing we're managing
    const childRole = effectiveUserRole || getApplicableChildRole();
    if (!childRole) return;

    const marginField = getMarginFieldName(childRole);
    if (!marginField) return;

    // Transform API products data to PricingItem format
    const items: PricingItem[] = currentProductsData.map((product: any) => {
      // Default margins based on role
      const defaultMargin =
        childRole === UserRole.SUPER_STOCKIST ? 10 :
        childRole === UserRole.DISTRIBUTOR ? 15 : 20;

      // Use product.basePrice or default to 1000 (since API doesn't return basePrice)
      const basePrice = product.basePrice || 1000;

      // Calculate final price with margin
      const finalPrice = basePrice * (1 + defaultMargin / 100);

      return {
        productId: product.id.toString(),
        productName: product.name,
        productCode: product.productCode,
        description: product.description,
        basePrice: basePrice,
        margin: defaultMargin,
        finalPrice: finalPrice,
        isCustomPrice: false,
        category: selectedCategory || 'All',
        selectedVariantId: undefined
      };
    });

    console.log('📊 [PRICING MANAGEMENT] Processed pricing items:', {
      itemCount: items.length,
      sampleItem: items[0]
    });

    setPricingItems(items);

    // Also set global pricing items with the same data initially
    setGlobalPricingItems([...items]);
  }, [productsData, categoryProductsData, selectedCategory, effectiveUserRole]);

  // Get the active pricing items based on the current tab
  const getActivePricingItems = () => {
    return activeTab === 'individual' ? pricingItems : globalPricingItems;
  };

  // Filter pricing items based on search query and category
  const getFilteredItems = () => {
    const activeItems = getActivePricingItems();

    return activeItems.filter(item => {
      // Apply search filter
      const matchesSearch = !searchQuery ||
        item.productName.toLowerCase().includes(searchQuery.toLowerCase());

      // Apply category filter
      const matchesCategory = !selectedCategory || item.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });
  };

  // Memoized filtered items for performance
  const filteredItems = getFilteredItems();

  // Handle variant selection
  const handleVariantSelect = (productId: string, variantId: string) => {
    console.log('🔄 [PRICING MANAGEMENT] Variant selected:', { productId, variantId });
    setSelectedVariants(prev => ({
      ...prev,
      [productId]: variantId
    }));

    // Update the pricing item with selected variant
    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          return {
            ...item,
            selectedVariantId: variantId
          };
        }
        return item;
      })
    );
  };

  // Handle global price update for all variants
  const handleGlobalPriceUpdate = (productId: string, variants: ProductVariant[], basePrice: number, margin: number) => {
    console.log('🌍 [PRICING MANAGEMENT] Global price update:', {
      productId,
      variantCount: variants.length,
      basePrice,
      margin
    });

    const finalPrice = basePrice * (1 + margin / 100);

    // Update global pricing items
    setGlobalPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          return {
            ...item,
            margin: margin,
            finalPrice: finalPrice,
            isCustomPrice: true,
            variants: variants
          };
        }
        return item;
      })
    );
  };

  // Update margin for a specific product and variant
  const updateMargin = (productId: string, variantId: string, margin: number) => {
    if (isNaN(margin) || margin < 0) return;

    console.log('📊 [PRICING MANAGEMENT] Updating margin:', { productId, variantId, margin });

    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          const finalPrice = item.basePrice * (1 + margin / 100);
          return {
            ...item,
            margin: margin,
            finalPrice,
            isCustomPrice: true,
            selectedVariantId: variantId
          };
        }
        return item;
      })
    );
  };

  // Toggle custom price for a product
  const toggleCustomPrice = (productId: string, enabled: boolean) => {
    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          return {
            ...item,
            isCustomPrice: enabled
          };
        }
        return item;
      })
    );
  };

  // Update custom price for a product and variant
  const updateCustomPrice = (productId: string, variantId: string, price: number) => {
    if (isNaN(price) || price <= 0) return;

    console.log('💰 [PRICING MANAGEMENT] Updating custom price:', { productId, variantId, price });

    setPricingItems(prev =>
      prev.map(item => {
        if (item.productId === productId) {
          const margin = ((price - item.basePrice) / item.basePrice) * 100;
          return {
            ...item,
            finalPrice: price,
            margin: parseFloat(margin.toFixed(2)),
            isCustomPrice: true,
            selectedVariantId: variantId
          };
        }
        return item;
      })
    );
  };

  // Apply global margin to all products
  const applyGlobalPricing = () => {
    const marginValue = parseFloat(globalMargin);
    const markupValue = parseFloat(globalMarkup);

    if ((isNaN(marginValue) || marginValue < 0) && (isNaN(markupValue) || markupValue < 0)) {
      Alert.alert('Please enter a valid margin percentage or markup amount');
      return;
    }

    setApplyingGlobalMargin(true);

    // Simulate API call with a delay
    setTimeout(() => {
      setPricingItems(prev =>
        prev.map(item => {
          let finalPrice = item.basePrice;
          let margin = 0;

          if (!isNaN(marginValue) && marginValue >= 0) {
            finalPrice = item.basePrice * (1 + marginValue / 100);
            margin = marginValue;
          } else if (!isNaN(markupValue) && markupValue >= 0) {
            finalPrice = item.basePrice + markupValue;
            margin = ((finalPrice - item.basePrice) / item.basePrice) * 100;
          }

          return {
            ...item,
            margin: parseFloat(margin.toFixed(2)),
            finalPrice,
            isCustomPrice: false
          };
        })
      );

      setApplyingGlobalMargin(false);
      setGlobalMargin('');
      setGlobalMarkup('');

      Alert.alert('Global pricing applied successfully');
    }, 1000);
  };

  // Save pricing changes
  const savePricing = async () => {
    if (!canEditPrices()) {
      Alert.alert('Error', 'You do not have permission to save pricing');
      return;
    }

    const activePricingItems = getActivePricingItems();
    if (activePricingItems.length === 0) {
      Alert.alert('Error', 'No pricing items to save');
      return;
    }

    setIsLoading(true);

    try {
      console.log('🚀 [PRICING MANAGEMENT] Saving pricing for items:', {
        itemCount: activePricingItems.length,
        activeTab,
        isBulkMode,
        currentUserId: currentUser?.id,
        userRole: currentUser?.role
      });

      // Process each pricing item
      const promises = activePricingItems.map(async (item) => {
        if (activeTab === 'global') {
          // Global pricing - create pricing for all variants
          if (item.variants && item.variants.length > 0) {
            const variantPromises = item.variants.map(variant =>
              handleCreatePricing(
                item.productId,
                variant.id.toString(),
                item.finalPrice,
                undefined, // Global pricing
                true // isGlobal flag
              )
            );
            return Promise.all(variantPromises);
          } else {
            console.warn('⚠️ [PRICING MANAGEMENT] No variants found for global pricing:', item.productId);
            return Promise.resolve();
          }
        } else {
          // Individual pricing - use selected variant
          const variantId = item.selectedVariantId;

          if (!variantId) {
            console.warn('⚠️ [PRICING MANAGEMENT] No variant selected for product:', item.productId);
            return Promise.resolve(); // Skip items without variant selection
          }

          return handleCreatePricing(
            item.productId,
            variantId,
            item.finalPrice,
            isBulkMode ? undefined : selectedUsers?.[0]?.id?.toString(), // For individual mode
            false // isGlobal flag
          );
        }
      });

      await Promise.all(promises);

      setIsLoading(false);
      Alert.alert('Success', `Pricing updated successfully for ${userName || 'selected users'}`);
      navigation.goBack();

    } catch (error: any) {
      setIsLoading(false);
      console.error('❌ [PRICING MANAGEMENT] Error saving pricing:', error);
      Alert.alert('Error', 'Failed to save pricing. Please try again.');
    }
  };

  // Handle create pricing for specific product/variant
  const handleCreatePricing = async (productId: string, variantId: string, price: number, targetUserId?: string, isGlobal: boolean = false) => {
    try {
      console.log('🚀 [PRICING MANAGEMENT] Creating pricing:', {
        productId,
        variantId,
        price,
        targetUserId,
        currentUserId: currentUser?.id,
        userRole: currentUser?.role
      });

      const basePayload = {
        id: parseInt(currentUser?.id || '0'),
        productId: parseInt(productId),
        variantId: parseInt(variantId),
        price: price,
      };

      if (currentUser?.role === UserRole.SUPER_STOCKIST) {
        // Create distributor price
        const distributorPayload: DistributorPriceRequest = {
          ...basePayload,
          ...(targetUserId ? { distributorId: parseInt(targetUserId) } : {})
        };

        const response = await createDistributorPrice({
          data: distributorPayload,
          isGlobal: isGlobal || !targetUserId
        }).unwrap();

        console.log('✅ [PRICING MANAGEMENT] Distributor Price Created:', response);
        return response;

      } else if (currentUser?.role === UserRole.DISTRIBUTOR) {
        // Create retailer price
        const retailerPayload: RetailerPriceRequest = {
          ...basePayload,
          ...(targetUserId ? { retailerId: parseInt(targetUserId) } : {})
        };

        const response = await createRetailerPrice({
          data: retailerPayload,
          isGlobal: isGlobal || !targetUserId
        }).unwrap();

        console.log('✅ [PRICING MANAGEMENT] Retailer Price Created:', response);
        return response;

      } else {
        throw new Error('You do not have permission to create pricing');
      }

    } catch (error: any) {
      console.error('❌ [PRICING MANAGEMENT] Error creating pricing:', error);
      throw error;
    }
  };

  // Check if user can edit prices
  const canEditPrices = (): boolean => {
    if (!currentUser) return false;

    // Only Ooge Team and Super Stockists can update prices
    return [UserRole.OOGE_TEAM, UserRole.SUPER_STOCKIST, UserRole.DISTRIBUTOR].includes(currentUser.role);
  };

  // Render header right actions
  const renderHeaderRightActions = () => (
    <IconButton
      icon="content-save"
      iconColor="white"
      size={24}
      onPress={savePricing}
      disabled={isLoading}
    />
  );

  // Category filter options
  const categoryOptions = [
    { id: 'all', label: 'All Categories' },
    ...categories.map(category => ({ id: category, label: category }))
  ];

  // Handle category filter change
  const handleCategoryFilterChange = (categoryId: string) => {
    setSelectedCategory(categoryId === 'all' ? null : categoryId);
  };

  // Render content
  const renderContent = () => {
    if (filteredItems.length === 0) {
      return (
        <EmptyState
          icon="attach-money"
          message={searchQuery || selectedCategory ? 'No products match your filters' : 'No products found'}
        />
      );
    }

    return (
      <FlatList
        data={filteredItems}
        renderItem={({ item }) => (
          <EnhancedPricingCard
            item={{
              ...item,
              productCode: item.productCode,
              description: item.description
            }}
            onMarginChange={updateMargin}
            onCustomPriceChange={updateCustomPrice}
            onVariantSelect={handleVariantSelect}
            onGlobalPriceUpdate={handleGlobalPriceUpdate}
            canEdit={canEditPrices()}
            isGlobalMode={activeTab === 'global'}
            globalMargin={parseFloat(globalMargin) || 0}
            showVariantSelection={activeTab === 'individual'}
          />
        )}
        keyExtractor={item => item.productId}
        contentContainerStyle={styles.pricingList}
        showsVerticalScrollIndicator={false}
      />
    );
  };

  // Render bulk users info
  const renderBulkUsersInfo = () => {
    if (!isBulkMode || !selectedUsers) return null;

    return (
      <Surface style={styles.bulkUsersContainer} elevation={1}>
        <Text variant="titleMedium" style={styles.bulkUsersTitle}>
          Managing Pricing for {selectedUsers.length} Users
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.userChipsContainer}>
          {selectedUsers.map((user: User) => (
            <Chip
              key={user.id}
              mode="flat"
              style={styles.userChip}
              textStyle={styles.userChipText}
            >
              {user.name}
            </Chip>
          ))}
        </ScrollView>
      </Surface>
    );
  };

  return (
    <BaseManagementScreen
      title={isBulkMode ? "Bulk Pricing Management" : "Pricing Management"}
      showBack={true}
      rightActions={renderHeaderRightActions()}
      subtitle={displayName}
      isLoading={isLoading}
      loadingText="Loading pricing data..."
    >
      {/* Bulk Users Info */}
      {renderBulkUsersInfo()}

      <Surface style={styles.tabContainer} elevation={1}>
        <View style={styles.tabs}>
          <Button
            mode={activeTab === 'individual' ? 'contained' : 'text'}
            onPress={() => switchTab('individual')}
            style={[styles.tab, activeTab === 'individual' && styles.activeTab]}
            labelStyle={activeTab === 'individual' ? styles.activeTabLabel : styles.tabLabel}
          >
            {isBulkMode ? 'Product Pricing' : 'Individual Pricing'}
          </Button>
          <Button
            mode={activeTab === 'global' ? 'contained' : 'text'}
            onPress={() => switchTab('global')}
            style={[styles.tab, activeTab === 'global' && styles.activeTab]}
            labelStyle={activeTab === 'global' ? styles.activeTabLabel : styles.tabLabel}
          >
            Global Pricing
          </Button>
        </View>
      </Surface>

      {/* Global Margin - Only show in Global tab */}
      {activeTab === 'global' && (
        <Surface style={styles.globalMarginContainer} elevation={1}>
          <Text variant="titleMedium" style={styles.globalMarginTitle}>
            {isBulkMode ? `Set Global Pricing for ${selectedUsers?.length} Users` : 'Set Global Pricing'}
          </Text>
          <View style={styles.globalMarginInputContainer}>
            <View style={styles.inputGroup}>
              <TextInput
                mode="outlined"
                label="Margin Percentage"
                placeholder="Enter margin percentage"
                value={globalMargin}
                onChangeText={setGlobalMargin}
                keyboardType="numeric"
                style={[styles.globalMarginInput, { marginBottom: 8 }]}
                right={<TextInput.Affix text="%" />}
                disabled={applyingGlobalMargin}
              />
              <TextInput
                mode="outlined"
                label="Markup Price"
                placeholder="Enter markup amount"
                value={globalMarkup}
                onChangeText={setGlobalMarkup}
                keyboardType="numeric"
                style={styles.globalMarginInput}
                right={<TextInput.Affix text="₹" />}
                disabled={applyingGlobalMargin}
              />
            </View>
            <Button
              mode="contained"
              onPress={applyGlobalPricing}
              disabled={(!globalMargin && !globalMarkup) || applyingGlobalMargin}
              style={styles.applyButton}
              loading={applyingGlobalMargin}
            >
              {isBulkMode ? 'Apply to All Users' : 'Apply'}
            </Button>
          </View>
        </Surface>
      )}

    {activeTab === 'individual' && (
        <>
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder="Search products..."
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              iconColor={theme.colors.primary}
            />
          </View>

          <View style={styles.filtersContainer}>
            <FilterChips
              options={categoryOptions}
              selectedId={selectedCategory || 'all'}
              onSelect={handleCategoryFilterChange}
            />
          </View>
          {renderContent()}
        </>
      )}
    </BaseManagementScreen>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: 'white',
    marginBottom: 8,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    borderRadius: 0,
  },
  activeTab: {
    backgroundColor: 'transparent',
  },
  tabLabel: {
    color: '#6b7280',
    fontSize: 14,
  },
  activeTabLabel: {
    color: '#6366f1',
    fontSize: 14,
    fontWeight: 'bold',
  },
  globalMarginContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  globalMarginTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
  },
  inputGroup: {
    width: 'auto',
    height: 120,
  },
  globalMarginInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  globalMarginInput: {
    flex: 1,
    marginRight: 12,
  },
  applyButton: {
    height: 50,
    justifyContent: 'center',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchBar: {
    elevation: 2,
    backgroundColor: 'white',
  },
  filtersContainer: {
    marginBottom: 8,
  },
  pricingList: {
    padding: 16,
    paddingBottom: 80,
  },
  bulkUsersContainer: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 8,
  },
  bulkUsersTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
    color: '#6366f1',
  },
  userChipsContainer: {
    flexDirection: 'row',
  },
  userChip: {
    marginRight: 8,
    backgroundColor: '#ddd6fe',
  },
  userChipText: {
    fontSize: 12,
    color: '#6366f1',
  },
});

export default PricingManagementScreen;
