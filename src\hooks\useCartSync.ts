import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetCartByUserIdQuery } from '../screens/cart/cartApi/apiSlice';
import { setCartFromServer, clearCart } from '../redux/slices/cartSlice';
import { useUser } from '../context/UserContext';
import type { RootState } from '../redux/store';

export const useCartSync = () => {
  const dispatch = useDispatch();
  const { currentUser } = useUser();
  const localCartItems = useSelector((state: RootState) => state.cart.items);
  const lastUpdated = useSelector((state: RootState) => state.cart.lastUpdated);

  const {
    data: cartData,
    error: cartError,
    refetch: refetchCart,
    isLoading,
  } = useGetCartByUserIdQuery(Number(currentUser?.id), {
    skip: !currentUser?.id,
    refetchOnMountOrArgChange: true,
  });

  useEffect(() => {
    console.log('🔄 [CART_SYNC] Effect triggered:', {
      hasCartData: !!cartData,
      hasError: !!cartError,
      localItemsCount: localCartItems.length,
      lastUpdated
    });
  
    // Handle 400/404 errors (cart not found)
    if (cartError && 'status' in cartError && 
        (cartError.status === 404 || cartError.status === 400)) {
      const errorMessage = cartError.data?.message?.toLowerCase() || '';
      
      if (errorMessage.includes('cart not found') || 
          errorMessage.includes('cart does not exist')) {
        console.log('🗑️ [CART_SYNC] Server cart not found (400/404), clearing local cart');
        dispatch(clearCart());
      }
    } 
    else if (cartData?.cartLine && Array.isArray(cartData.cartLine)) {
      // Sync server cart to local state
      const serverItems = cartData.cartLine.map((item: any) => ({
        id: item.productId,
        name: item.productName,
        price: `₹${item?.price || 0}`,
        quantity: item.quantity,
        image: '',
        variant: item.variantName,
        cartLineId: item.id,
        variantId: item.variantId
      }));
      
      // Always update to ensure consistency
      console.log('🔄 [CART_SYNC] Syncing server cart to local:', {
        serverCount: serverItems.length,
        localCount: localCartItems.length
      });
      dispatch(setCartFromServer(serverItems));
    } 
    else if (cartData === null && localCartItems.length > 0) {
      // Server returned null but we have local items, clear them
      console.log('🗑️ [CART_SYNC] Server returned null, clearing local cart');
      dispatch(clearCart());
    }
  }, [cartData, cartError, dispatch, localCartItems.length, lastUpdated]);

  return { 
    refetchCart, 
    isLoading, 
    cartError,
    serverCartCount: cartData?.cartLine?.length || 0,
    localCartCount: localCartItems.length
  };
};