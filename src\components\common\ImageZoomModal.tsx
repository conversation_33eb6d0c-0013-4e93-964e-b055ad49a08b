// import React, { useState, useEffect } from 'react';
// import {
//   View,
//   Text,
//   TouchableOpacity,
//   Image,
//   Modal,
//   Dimensions,
//   StyleSheet,
// } from 'react-native';
// import Icon from 'react-native-vector-icons/MaterialIcons';
// import {
//   GestureHandlerRootView,
//   PinchGestureHandler,
//   PanGestureHandler,
// } from 'react-native-gesture-handler';
// import Animated, {
//   useAnimatedGestureHandler,
//   useAnimatedStyle,
//   useSharedValue,
//   withSpring,
//   withTiming,
//   runOnJS,
// } from 'react-native-reanimated';

// const { width: screenWidth } = Dimensions.get('window');

// interface ImageItem {
//   id: string;
//   uri: string;
// }

// interface ImageZoomModalProps {
//   visible: boolean;
//   images: ImageItem[];
//   initialIndex: number;
//   onClose: () => void;
//   title?: string;
// }

// const ImageZoomModal: React.FC<ImageZoomModalProps> = ({
//   visible,
//   images,
//   initialIndex,
//   onClose,
//   title,
// }) => {
//   const [currentIndex, setCurrentIndex] = useState(initialIndex);

//   // Animation values
//   const scale = useSharedValue(1);
//   const translateX = useSharedValue(0);
//   const translateY = useSharedValue(0);
//   const opacity = useSharedValue(0);

//   useEffect(() => {
//     if (visible) {
//       setCurrentIndex(initialIndex);
//       opacity.value = withTiming(1, { duration: 300 });
//     }
//   }, [visible, initialIndex]);

//   useEffect(() => {
//     resetZoom();
//   }, [currentIndex]);

//   const closeModal = () => {
//     opacity.value = withTiming(0, { duration: 300 });
//     setTimeout(() => {
//       onClose();
//       resetZoom();
//     }, 300);
//   };

//   const resetZoom = () => {
//     scale.value = withSpring(1);
//     translateX.value = withSpring(0);
//     translateY.value = withSpring(0);
//   };

//   const navigateImage = (direction: 'left' | 'right') => {
//     const maxIndex = images.length - 1;
//     let newIndex = currentIndex;

//     if (direction === 'left' && currentIndex > 0) {
//       newIndex = currentIndex - 1;
//     } else if (direction === 'right' && currentIndex < maxIndex) {
//       newIndex = currentIndex + 1;
//     }

//     if (newIndex !== currentIndex) {
//       setCurrentIndex(newIndex);
//       resetZoom();
//     }
//   };

//   // Pinch gesture handler for zoom
//   const pinchHandler = useAnimatedGestureHandler({
//     onStart: (_, context: any) => {
//       context.startScale = scale.value;
//     },
//     onActive: (event, context) => {
//       scale.value = Math.max(1, Math.min(context.startScale * event.scale, 5));
//     },
//     onEnd: () => {
//       if (scale.value < 1.2) {
//         scale.value = withSpring(1);
//         translateX.value = withSpring(0);
//         translateY.value = withSpring(0);
//       }
//     },
//   });

//   // Pan gesture handler for moving zoomed image
//   const panHandler = useAnimatedGestureHandler({
//     onStart: (_, context: any) => {
//       context.startX = translateX.value;
//       context.startY = translateY.value;
//     },
//     onActive: (event, context) => {
//       if (scale.value > 1) {
//         const maxTranslateX = (screenWidth * (scale.value - 1)) / 2;
//         const maxTranslateY = (400 * (scale.value - 1)) / 2;

//         translateX.value = Math.max(
//           -maxTranslateX,
//           Math.min(maxTranslateX, context.startX + event.translationX)
//         );
//         translateY.value = Math.max(
//           -maxTranslateY,
//           Math.min(maxTranslateY, context.startY + event.translationY)
//         );
//       } else {
//         // Handle horizontal swipe for image navigation when not zoomed
//         translateX.value = context.startX + event.translationX;
//       }
//     },
//     onEnd: (event) => {
//       if (scale.value <= 1) {
//         // Handle swipe navigation
//         if (Math.abs(event.translationX) > 100) {
//           if (event.translationX > 0) {
//             runOnJS(navigateImage)('left');
//           } else {
//             runOnJS(navigateImage)('right');
//           }
//         }
//         translateX.value = withSpring(0);
//         translateY.value = withSpring(0);
//       }
//     },
//   });

//   const animatedStyle = useAnimatedStyle(() => {
//     return {
//       transform: [
//         { scale: scale.value },
//         { translateX: translateX.value },
//         { translateY: translateY.value },
//       ],
//     };
//   });

//   const modalAnimatedStyle = useAnimatedStyle(() => {
//     return {
//       opacity: opacity.value,
//     };
//   });

//   if (!visible || images.length === 0) {
//     return null;
//   }

//   const currentImage = images[currentIndex];

//   return (
//     <Modal
//       visible={visible}
//       transparent={true}
//       onRequestClose={closeModal}
//       statusBarTranslucent={true}
//     >
//       <GestureHandlerRootView style={styles.container}>
//         <Animated.View style={[styles.modalContainer, modalAnimatedStyle]}>
//           {/* Header with navigation and close button */}
//           <View style={styles.header}>
//             <TouchableOpacity
//               style={[
//                 styles.navButton,
//                 { opacity: currentIndex > 0 ? 1 : 0.3 },
//               ]}
//               onPress={() => navigateImage('left')}
//               disabled={currentIndex === 0}
//             >
//               <Icon name="chevron-left" size={32} color="white" />
//             </TouchableOpacity>

//             <View style={styles.headerCenter}>
//               {title && <Text style={styles.titleText}>{title}</Text>}
//               <View style={styles.imageCounter}>
//                 <Text style={styles.imageCounterText}>
//                   {currentIndex + 1} / {images.length}
//                 </Text>
//               </View>
//             </View>

//             <TouchableOpacity
//               style={[
//                 styles.navButton,
//                 { opacity: currentIndex < images.length - 1 ? 1 : 0.3 },
//               ]}
//               onPress={() => navigateImage('right')}
//               disabled={currentIndex === images.length - 1}
//             >
//               <Icon name="chevron-right" size={32} color="white" />
//             </TouchableOpacity>
//           </View>

//           {/* Zoomable Image */}
//           <View style={styles.imageContainer}>
//             <PanGestureHandler onGestureEvent={panHandler}>
//               <Animated.View>
//                 <PinchGestureHandler onGestureEvent={pinchHandler}>
//                   <Animated.View style={animatedStyle}>
//                     <Image
//                       source={{ uri: currentImage.uri }}
//                       style={styles.zoomedImage}
//                       resizeMode="contain"
//                     />
//                   </Animated.View>
//                 </PinchGestureHandler>
//               </Animated.View>
//             </PanGestureHandler>
//           </View>

//           {/* Bottom controls */}
//           <View style={styles.footer}>
//             <TouchableOpacity style={styles.controlButton} onPress={resetZoom}>
//               <Icon name="zoom-out-map" size={24} color="white" />
//               <Text style={styles.controlText}>Reset</Text>
//             </TouchableOpacity>

//             <TouchableOpacity style={styles.controlButton} onPress={closeModal}>
//               <Icon name="close" size={24} color="white" />
//               <Text style={styles.controlText}>Close</Text>
//             </TouchableOpacity>
//           </View>

//           {/* Swipe instruction */}
//           <View style={styles.instructionContainer}>
//             <Text style={styles.instructionText}>
//               Pinch to zoom • Swipe to navigate • Drag to move
//             </Text>
//           </View>

//           {/* Image thumbnails */}
//           {images.length > 1 && (
//             <View style={styles.thumbnailContainer}>
//               {images.map((image, index) => (
//                 <TouchableOpacity
//                   key={image.id}
//                   style={[
//                     styles.thumbnail,
//                     index === currentIndex && styles.activeThumbnail,
//                   ]}
//                   onPress={() => setCurrentIndex(index)}
//                 >
//                   <Image source={{ uri: image.uri }} style={styles.thumbnailImage} />
//                 </TouchableOpacity>
//               ))}
//             </View>
//           )}
//         </Animated.View>
//       </GestureHandlerRootView>
//     </Modal>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   modalContainer: {
//     flex: 1,
//     backgroundColor: 'rgba(0,0,0,0.95)',
//     justifyContent: 'center',
//   },
//   header: {
//     position: 'absolute',
//     top: 50,
//     left: 0,
//     right: 0,
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     paddingHorizontal: 20,
//     zIndex: 1000,
//   },
//   headerCenter: {
//     alignItems: 'center',
//   },
//   titleText: {
//     color: 'white',
//     fontSize: 16,
//     fontWeight: '600',
//     marginBottom: 4,
//   },
//   navButton: {
//     backgroundColor: 'rgba(255,255,255,0.2)',
//     padding: 8,
//     borderRadius: 20,
//   },
//   imageCounter: {
//     backgroundColor: 'rgba(0,0,0,0.7)',
//     paddingHorizontal: 16,
//     paddingVertical: 8,
//     borderRadius: 20,
//   },
//   imageCounterText: {
//     color: 'white',
//     fontSize: 16,
//     fontWeight: '600',
//   },
//   imageContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   zoomedImage: {
//     width: screenWidth,
//     height: 400,
//   },
//   footer: {
//     position: 'absolute',
//     bottom: 120,
//     left: 0,
//     right: 0,
//     flexDirection: 'row',
//     justifyContent: 'space-around',
//     paddingHorizontal: 40,
//   },
//   controlButton: {
//     backgroundColor: 'rgba(255,255,255,0.2)',
//     paddingHorizontal: 20,
//     paddingVertical: 12,
//     borderRadius: 25,
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   controlText: {
//     color: 'white',
//     marginLeft: 8,
//     fontSize: 16,
//     fontWeight: '500',
//   },
//   instructionContainer: {
//     position: 'absolute',
//     bottom: 80,
//     left: 0,
//     right: 0,
//     alignItems: 'center',
//   },
//   instructionText: {
//     color: 'rgba(255,255,255,0.7)',
//     fontSize: 14,
//     textAlign: 'center',
//   },
//   thumbnailContainer: {
//     position: 'absolute',
//     bottom: 20,
//     left: 0,
//     right: 0,
//     flexDirection: 'row',
//     justifyContent: 'center',
//     paddingHorizontal: 20,
//   },
//   thumbnail: {
//     width: 40,
//     height: 40,
//     marginHorizontal: 4,
//     borderRadius: 8,
//     borderWidth: 2,
//     borderColor: 'transparent',
//     overflow: 'hidden',
//   },
//   activeThumbnail: {
//     borderColor: '#6366f1',
//   },
//   thumbnailImage: {
//     width: '100%',
//     height: '100%',
//     resizeMode: 'cover',
//   },
// });

// export default ImageZoomModal;


import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Modal,
  Dimensions,
  StyleSheet,
  StatusBar,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {
  GestureHandlerRootView,
  PinchGestureHandler,
  PanGestureHandler,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageItem {
  id: string;
  uri: string;
}

interface ImageZoomModalProps {
  visible: boolean;
  images: ImageItem[];
  initialIndex: number;
  onClose: () => void;
}

const ImageZoomModal: React.FC<ImageZoomModalProps> = ({
  visible,
  images,
  initialIndex,
  onClose,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
    }
  }, [visible, initialIndex]);

  useEffect(() => {
    resetZoom();
  }, [currentIndex]);

  const resetZoom = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
  };

  const navigateImage = (direction: 'left' | 'right') => {
    const maxIndex = images.length - 1;
    let newIndex = currentIndex;

    if (direction === 'left' && currentIndex > 0) {
      newIndex = currentIndex - 1;
    } else if (direction === 'right' && currentIndex < maxIndex) {
      newIndex = currentIndex + 1;
    }

    if (newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
    }
  };

  // Pinch gesture handler for zoom
  const pinchHandler = useAnimatedGestureHandler({
    onStart: (_, context: any) => {
      context.startScale = scale.value;
    },
    onActive: (event, context) => {
      scale.value = Math.max(1, Math.min(context.startScale * event.scale, 4));
    },
    onEnd: () => {
      if (scale.value < 1.2) {
        scale.value = withSpring(1);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      }
    },
  });

  // Pan gesture handler for moving zoomed image
  const panHandler = useAnimatedGestureHandler({
    onStart: (_, context: any) => {
      context.startX = translateX.value;
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      if (scale.value > 1) {
        // When zoomed, allow panning
        const maxTranslateX = (screenWidth * (scale.value - 1)) / 2;
        const maxTranslateY = (screenHeight * (scale.value - 1)) / 2;
        
        translateX.value = Math.max(
          -maxTranslateX,
          Math.min(maxTranslateX, context.startX + event.translationX)
        );
        translateY.value = Math.max(
          -maxTranslateY,
          Math.min(maxTranslateY, context.startY + event.translationY)
        );
      } else {
        // Handle horizontal swipe for image navigation when not zoomed
        translateX.value = context.startX + event.translationX;
      }
    },
    onEnd: (event) => {
      if (scale.value <= 1) {
        // Handle swipe navigation
        if (Math.abs(event.translationX) > 100) {
          if (event.translationX > 0) {
            runOnJS(navigateImage)('left');
          } else {
            runOnJS(navigateImage)('right');
          }
        }
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  if (!visible || images.length === 0) {
    return null;
  }

  const currentImage = images[currentIndex];

  return (
    <Modal
      visible={visible}
      transparent={false}
      onRequestClose={onClose}
      statusBarTranslucent={false}
      animationType="fade"
    >
      <StatusBar backgroundColor="white" barStyle="dark-content" />
      <GestureHandlerRootView style={styles.container}>
        <View style={styles.fullScreenContainer}>
          {/* Header with close button and counter */}
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Icon name="close" size={24} color="#333" />
            </TouchableOpacity>
            
            <Text style={styles.counterText}>
              {currentIndex + 1} of {images.length}
            </Text>
            
            <View style={styles.placeholder} />
          </View>

          {/* Full Screen Image */}
          <View style={styles.imageContainer}>
            <PanGestureHandler onGestureEvent={panHandler}>
              <Animated.View style={styles.imageWrapper}>
                <PinchGestureHandler onGestureEvent={pinchHandler}>
                  <Animated.View style={animatedStyle}>
                    <TouchableOpacity 
                      activeOpacity={1}
                      onPress={() => {
                        if (scale.value > 1) {
                          resetZoom();
                        }
                      }}
                    >
                      <Image
                        source={{ uri: currentImage.uri }}
                        style={styles.fullScreenImage}
                        resizeMode="contain"
                      />
                    </TouchableOpacity>
                  </Animated.View>
                </PinchGestureHandler>
              </Animated.View>
            </PanGestureHandler>
          </View>
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f8f8f8',
  },
  counterText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  placeholder: {
    width: 40, // Same width as close button for balance
  },
  imageContainer: {
    flex: 1,
    backgroundColor: '#fafafa',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageWrapper: {
    width: screenWidth,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: screenWidth - 20,
    height: screenHeight - 100, // Account for header
    maxWidth: screenWidth - 20,
    maxHeight: screenHeight - 100,
  },
});

export default ImageZoomModal;
