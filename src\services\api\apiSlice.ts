import { createApi } from '@reduxjs/toolkit/query/react';
import AuthApiService from './AuthApiService';

// Define a service using a base URL and expected endpoints
export const apiSlice = createApi({
  reducerPath: 'api',
  // Custom base query function that uses AuthApiService
  baseQuery: async ({ url, method, body, params }) => {
    try {
      let result;
      switch (method?.toUpperCase() || 'GET') {
        case 'GET':
          result = await AuthApiService.get(url, params);
          break;
        case 'POST':
          result = await AuthApiService.post(url, body);
          break;
        case 'PUT':
          result = await AuthApiService.put(url, body);
          break;
        case 'DELETE':
          result = await AuthApiService.delete(url);
          break;
        default:
          result = await AuthApiService.get(url, params);
      }
      return { data: result };
    } catch (error: any) {
      return {
        error: {
          status: error.response?.status,
          data: error.response?.data || error.message
        }
      };
    }
  },
  // The "endpoints" represent operations and requests for this server
  endpoints: (builder) => ({
    // Categories
    getCategories: builder.query({
      query: (params) => ({
        url: 'api/v1/catalog/categories',
        method: 'GET',
        params: { page: 0, count: 10, type: 1, ...params }
      }),
      transformResponse: (response) => response?.data || []
    }),

    // Products
    getProducts: builder.query({
      query: (params) => ({
        url: 'api/v1/catalog/categories',
        method: 'GET',
        params : { page: 0, count: 100, type: 2,...params }
      }),
      transformResponse: (response) => response?.data || []
    }),

    getProductsByCategory: builder.query({
      query: (categoryId) => ({
        url: `api/v1/catalog/products/${categoryId}`,
        method: 'GET',
      }),
      transformResponse: (response) => {
        console.log('Products response:', response?.data);
        return response.data || [];
      }
    }),

    getFilteredProducts: builder.query({
      query: (params) => ({
        url: 'api/v1/catalog/filters',
        method: 'GET',
        params: {
          name: params.searchQuery || '',
          status: 1,
          type: 1,
          ...params
        }
      }),
      transformResponse: (response) => response.data?.data || []
    }),

    getProductById: builder.query({
      query: (categoryId) => ({
        url: `api/v1/catalog/product/${categoryId}`,
        method: 'GET'
      }),
      transformResponse: (response) => response.data.productDTO || {}
    }),
    getVariantsbyProductId: builder.query({
      query: (productId) => ({
        url: `api/v1/variant/variants/${productId}`,
        method: 'GET'
      }),
      transformResponse: (response) => response.data || {}
    }),
    getPricingbyProductAndVariantId: builder.query({
      query: ({productId, variantId}) => ({
        url: `api/v1/pricing/products-prices/${productId}?variantId=${variantId}`,
        method: 'GET'
      }),
      transformResponse: (response) => response.data || {}
    }),
    // Authentication - this will use the existing authenticate method
    login: builder.mutation({
      queryFn: async ({ username, password }) => {
        try {
          const user = await AuthApiService.authenticate(username, password);
          return { data: user };
        } catch (error: any) {
          return {
            error: {
              status: error.response?.status,
              data: error.response?.data || error.message
            }
          };
        }
      }
    }),
    getAllProductPricing: builder.query({
      query: (params) => ({
        url: 'api/v1/pricing/products-prices',
        method: 'GET',
        params: { page: 0, count: 10, ...params }
      }),
      transformResponse: (response) => response?.data || []
    }),
  }),
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetCategoriesQuery,
  useGetProductsQuery,
  useGetProductsByCategoryQuery,
  useGetFilteredProductsQuery,
  useGetProductByIdQuery,
  useGetVariantsbyProductIdQuery,
  useGetPricingbyProductAndVariantIdQuery,
  useLoginMutation,
} = apiSlice;
