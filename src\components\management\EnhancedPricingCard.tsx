import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { Card, Text, TextInput, Button, Chip, IconButton, Menu, Divider, ActivityIndicator, Surface, HelperText } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useGetVariantsbyProductIdQuery, useGetPricingbyProductAndVariantIdQuery } from '../../services/api/apiSlice';

interface ProductVariant {
  id: number;
  unitOfMeasurement: string;
  quantity: number;
  catalogType: string;
  urls?: string[];
  price?: number;
  sellingPrice?: number;
}

interface PricingItem {
  productId: string;
  productName: string;
  productCode?: string;
  description?: string;
  basePrice: number;
  margin: number;
  finalPrice: number;
  isCustomPrice: boolean;
  category: string;
  variants?: ProductVariant[];
  selectedVariantId?: string;
  targetUserId?: string;
}

interface EnhancedPricingCardProps {
  item: PricingItem;
  onMarginChange: (productId: string, variantId: string, margin: number) => void;
  onCustomPriceChange: (productId: string, variantId: string, price: number) => void;
  onVariantSelect: (productId: string, variantId: string) => void;
  onGlobalPriceUpdate?: (productId: string, variants: ProductVariant[], basePrice: number, margin: number) => void;
  canEdit: boolean;
  isGlobalMode?: boolean;
  globalMargin?: number;
  showVariantSelection?: boolean;
}

const EnhancedPricingCard: React.FC<EnhancedPricingCardProps> = ({
  item,
  onMarginChange,
  onCustomPriceChange,
  onVariantSelect,
  onGlobalPriceUpdate,
  canEdit,
  isGlobalMode = false,
  globalMargin = 0,
  showVariantSelection = true
}) => {
  const [showVariantMenu, setShowVariantMenu] = useState(false);
  const [localMargin, setLocalMargin] = useState(isGlobalMode ? globalMargin.toString() : item.margin.toString());
  const [localPrice, setLocalPrice] = useState(item.finalPrice.toString());
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [allVariants, setAllVariants] = useState<ProductVariant[]>([]);
  const [isUpdatingPrice, setIsUpdatingPrice] = useState(false);

  // Get variants for the product
  const {
    data: variantsData,
    isLoading: isVariantsLoading,
    error: variantsError
  } = useGetVariantsbyProductIdQuery(parseInt(item.productId), {
    skip: !item.productId
  });

  // Handle variants error
  useEffect(() => {
    if (variantsError) {
      setHasError(true);
      setErrorMessage('Failed to load product variants. Please try again.');
      console.error('❌ [PRICING CARD] Variants error:', variantsError);
    } else {
      setHasError(false);
      setErrorMessage('');
    }
  }, [variantsError]);

  // Get pricing for selected variant
  const {
    data: pricingData,
    isLoading: isPricingLoading,
    error: pricingError
  } = useGetPricingbyProductAndVariantIdQuery(
    {
      productId: parseInt(item.productId),
      variantId: selectedVariant?.id || 0
    },
    {
      skip: !selectedVariant?.id
    }
  );

  // Handle API errors
  useEffect(() => {
    if (variantsError) {
      setHasError(true);
      setErrorMessage('Failed to load product variants');
      console.error('❌ [PRICING CARD] Variants error:', variantsError);
    } else if (pricingError) {
      setHasError(true);
      setErrorMessage('Failed to load pricing data');
      console.error('❌ [PRICING CARD] Pricing error:', pricingError);
    } else {
      setHasError(false);
      setErrorMessage('');
    }
  }, [variantsError, pricingError]);

  // Initialize selected variant and store all variants
  useEffect(() => {
    if (variantsData?.productVariantDetailsList && variantsData.productVariantDetailsList.length > 0) {
      const variants = variantsData.productVariantDetailsList;
      setAllVariants(variants);

      const firstVariant = variants[0];
      setSelectedVariant(firstVariant);

      // If no variant is selected and not in global mode, select the first one
      if (!item.selectedVariantId && !isGlobalMode && showVariantSelection) {
        onVariantSelect(item.productId, firstVariant.id.toString());
      }
    }
  }, [variantsData, item.productId, item.selectedVariantId, onVariantSelect, isGlobalMode, showVariantSelection]);

  // Update base price when pricing data is loaded
  useEffect(() => {
    if (pricingData?.priceVariantDetailsDTO && pricingData.priceVariantDetailsDTO.length > 0) {
      const priceInfo = pricingData.priceVariantDetailsDTO[0];
      const basePrice = priceInfo.price || priceInfo.sellingPrice || 0;

      // Update local price if it's not custom
      if (!item.isCustomPrice) {
        const newFinalPrice = basePrice * (1 + item.margin / 100);
        setLocalPrice(newFinalPrice.toString());
      }
    }
  }, [pricingData, item.margin, item.isCustomPrice]);

  const handleMarginChange = (value: string) => {
    setLocalMargin(value);
    const marginValue = parseFloat(value);

    if (!isNaN(marginValue) && marginValue >= 0) {
      setIsUpdatingPrice(true);

      // Calculate new price based on base price and margin
      const basePrice = getBasePriceFromPricing();
      const newFinalPrice = basePrice * (1 + marginValue / 100);
      setLocalPrice(newFinalPrice.toFixed(2));

      if (isGlobalMode && onGlobalPriceUpdate) {
        // In global mode, update all variants for this product
        onGlobalPriceUpdate(item.productId, allVariants, basePrice, marginValue);
      } else if (selectedVariant && showVariantSelection) {
        // In individual mode, update specific variant
        onMarginChange(item.productId, selectedVariant.id.toString(), marginValue);
      }

      setTimeout(() => setIsUpdatingPrice(false), 300);
    }
  };

  const handlePriceChange = (value: string) => {
    setLocalPrice(value);
    const priceValue = parseFloat(value);

    if (!isNaN(priceValue) && priceValue > 0) {
      // Calculate margin from price
      const basePrice = getBasePriceFromPricing();
      const calculatedMargin = ((priceValue - basePrice) / basePrice) * 100;
      setLocalMargin(calculatedMargin.toFixed(2));

      if (isGlobalMode && onGlobalPriceUpdate) {
        // In global mode, update all variants for this product
        onGlobalPriceUpdate(item.productId, allVariants, basePrice, calculatedMargin);
      } else if (selectedVariant && showVariantSelection) {
        // In individual mode, update specific variant
        onCustomPriceChange(item.productId, selectedVariant.id.toString(), priceValue);
      }
    }
  };

  const handleVariantSelect = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setShowVariantMenu(false);
    onVariantSelect(item.productId, variant.id.toString());
  };

  const getVariantDisplayText = (variant: ProductVariant): string => {
    return `${variant.unitOfMeasurement} (${variant.quantity} available)`;
  };

  const getBasePriceFromPricing = (): number => {
    if (pricingData?.priceVariantDetailsDTO && pricingData.priceVariantDetailsDTO.length > 0) {
      const priceInfo = pricingData.priceVariantDetailsDTO[0];
      return priceInfo.price || priceInfo.sellingPrice || 0;
    }
    return item.basePrice;
  };

  const calculateDiscount = (): number => {
    if (pricingData?.priceVariantDetailsDTO && pricingData.priceVariantDetailsDTO.length > 0) {
      const priceInfo = pricingData.priceVariantDetailsDTO[0];
      if (priceInfo.price && priceInfo.sellingPrice && priceInfo.price > priceInfo.sellingPrice) {
        return ((priceInfo.price - priceInfo.sellingPrice) / priceInfo.price) * 100;
      }
    }
    return 0;
  };

  const basePrice = getBasePriceFromPricing();
  const discount = calculateDiscount();

  // Show error state if there are API errors
  if (hasError) {
    return (
      <Card style={[styles.card, styles.errorCard]} elevation={2}>
        <Card.Content>
          <View style={styles.errorContainer}>
            <Icon name="error-outline" size={24} color="#ef4444" />
            <Text style={styles.errorText}>{errorMessage}</Text>
            <Text style={styles.productName}>{item.productName}</Text>
          </View>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={[styles.card, isGlobalMode && styles.globalCard]} elevation={2}>
      <Card.Content>
        {/* Global Mode Indicator */}
        {isGlobalMode && (
          <Surface style={styles.globalIndicator} elevation={1}>
            <Icon name="public" size={16} color="#6366f1" />
            <Text style={styles.globalText}>Global Pricing</Text>
          </Surface>
        )}

        {/* Product Header */}
        <View style={styles.header}>
          <View style={styles.productInfo}>
            <Text variant="titleMedium" style={styles.productName}>
              {item.productName}
            </Text>
            {item.productCode && (
              <Text variant="bodySmall" style={styles.productCode}>
                SKU: {item.productCode}
              </Text>
            )}
            {item.description && (
              <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
                {item.description}
              </Text>
            )}
          </View>
          <View style={styles.headerRight}>
            <Chip mode="flat" style={styles.categoryChip}>
              <Text style={styles.categoryText}>{item.category}</Text>
            </Chip>
            {isGlobalMode && (
              <Text style={styles.variantCount}>
                {allVariants.length} variant{allVariants.length !== 1 ? 's' : ''}
              </Text>
            )}
          </View>
        </View>

        <Divider style={styles.divider} />

        {/* Variant Selection - Only show in individual mode */}
        {showVariantSelection && !isGlobalMode && (
          <View style={styles.variantSection}>
            <Text variant="titleSmall" style={styles.sectionTitle}>
              Product Variant
            </Text>

            {isVariantsLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" />
                <Text style={styles.loadingText}>Loading variants...</Text>
              </View>
            ) : variantsData?.productVariantDetailsList && variantsData.productVariantDetailsList.length > 0 ? (
              <Menu
                visible={showVariantMenu}
                onDismiss={() => setShowVariantMenu(false)}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setShowVariantMenu(true)}
                    style={styles.variantButton}
                    contentStyle={styles.variantButtonContent}
                    disabled={!canEdit}
                  >
                    <View style={styles.variantButtonText}>
                      <Text style={styles.variantText}>
                        {selectedVariant ? getVariantDisplayText(selectedVariant) : 'Select Variant'}
                      </Text>
                      <Icon name="arrow-drop-down" size={20} color="#6366f1" />
                    </View>
                  </Button>
                }
              >
                {variantsData.productVariantDetailsList.map((variant: any) => (
                  <Menu.Item
                    key={variant.id}
                    onPress={() => handleVariantSelect(variant)}
                    title={getVariantDisplayText(variant)}
                    titleStyle={selectedVariant?.id === variant.id ? styles.selectedVariantText : undefined}
                  />
                ))}
              </Menu>
            ) : (
              <Text style={styles.noVariantsText}>No variants available</Text>
            )}
          </View>
        )}

        {/* Global Mode Variants Summary */}
        {isGlobalMode && allVariants.length > 0 && (
          <View style={styles.variantSection}>
            <Text variant="titleSmall" style={styles.sectionTitle}>
              All Product Variants ({allVariants.length})
            </Text>
            <View style={styles.variantsList}>
              {allVariants.slice(0, 3).map((variant, index) => (
                <Chip key={variant.id} mode="flat" style={styles.variantChip}>
                  <Text style={styles.variantChipText}>{variant.unitOfMeasurement}</Text>
                </Chip>
              ))}
              {allVariants.length > 3 && (
                <Chip mode="flat" style={styles.variantChip}>
                  <Text style={styles.variantChipText}>+{allVariants.length - 3} more</Text>
                </Chip>
              )}
            </View>
          </View>
        )}

        <Divider style={styles.divider} />

        {/* Pricing Information */}
        <View style={styles.pricingSection}>
          <Text variant="titleSmall" style={styles.sectionTitle}>
            Pricing Information
          </Text>

          {isPricingLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" />
              <Text style={styles.loadingText}>Loading pricing...</Text>
            </View>
          ) : pricingError ? (
            <Text style={styles.errorText}>Error loading pricing</Text>
          ) : (
            <View style={styles.priceInfo}>
              <View style={styles.priceRow}>
                <Text style={styles.priceLabel}>Base Price:</Text>
                <Text style={styles.basePrice}>₹{basePrice.toFixed(2)}</Text>
              </View>

              {discount > 0 && (
                <View style={styles.priceRow}>
                  <Text style={styles.priceLabel}>Discount:</Text>
                  <Text style={styles.discountText}>{discount.toFixed(1)}% off</Text>
                </View>
              )}

              <View style={styles.priceRow}>
                <Text style={styles.priceLabel}>Current Selling Price:</Text>
                <Text style={styles.sellingPrice}>
                  ₹{pricingData?.priceVariantDetailsDTO?.[0]?.sellingPrice?.toFixed(2) || basePrice.toFixed(2)}
                </Text>
              </View>
            </View>
          )}
        </View>

        <Divider style={styles.divider} />

        {/* Margin and Final Price Controls */}
        {canEdit && (isGlobalMode || selectedVariant) && (
          <View style={styles.controlsSection}>
            <Text variant="titleSmall" style={styles.sectionTitle}>
              {isGlobalMode ? 'Set Global Pricing' : 'Set Your Pricing'}
            </Text>

            <View style={styles.inputRow}>
              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Margin %"
                  value={localMargin}
                  onChangeText={handleMarginChange}
                  keyboardType="numeric"
                  style={[styles.input, isUpdatingPrice && styles.updatingInput]}
                  right={
                    <TextInput.Affix text={
                      isUpdatingPrice ? (
                        <ActivityIndicator size={16} color="#6366f1" />
                      ) : (
                        '%'
                      )
                    } />
                  }
                  disabled={!canEdit}
                />
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label={isGlobalMode ? 'Global Price' : 'Final Price'}
                  value={localPrice}
                  onChangeText={handlePriceChange}
                  keyboardType="numeric"
                  style={[styles.input, isUpdatingPrice && styles.updatingInput]}
                  left={<TextInput.Affix text="₹" />}
                  disabled={!canEdit}
                />
              </View>
            </View>

            <View style={[styles.finalPriceDisplay, isGlobalMode && styles.globalPriceDisplay]}>
              <View style={styles.priceDisplayLeft}>
                <Text style={styles.finalPriceLabel}>
                  {isGlobalMode ? 'Global Selling Price:' : 'Your Selling Price:'}
                </Text>
                {isGlobalMode && (
                  <Text style={styles.globalNote}>
                    Applied to all {allVariants.length} variant{allVariants.length !== 1 ? 's' : ''}
                  </Text>
                )}
              </View>
              <Text style={[styles.finalPriceValue, isGlobalMode && styles.globalPriceValue]}>
                ₹{parseFloat(localPrice).toFixed(2)}
              </Text>
            </View>

            {isGlobalMode && (
              <Surface style={styles.globalWarning} elevation={1}>
                <Icon name="info" size={16} color="#f59e0b" />
                <Text style={styles.globalWarningText}>
                  This pricing will be applied globally to all variants of this product
                </Text>
              </Surface>
            )}
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  productInfo: {
    flex: 1,
    marginRight: 12,
  },
  productName: {
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  productCode: {
    color: '#6b7280',
    marginBottom: 4,
  },
  description: {
    color: '#6b7280',
    lineHeight: 18,
  },
  categoryChip: {
    backgroundColor: '#eef2ff',
  },
  categoryText: {
    color: '#6366f1',
    fontSize: 12,
  },
  divider: {
    marginVertical: 12,
  },
  variantSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  loadingText: {
    marginLeft: 8,
    color: '#6b7280',
  },
  errorText: {
    color: '#ef4444',
    fontStyle: 'italic',
  },
  variantButton: {
    borderColor: '#6366f1',
  },
  variantButtonContent: {
    height: 40,
  },
  variantButtonText: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  variantText: {
    color: '#333',
    flex: 1,
  },
  selectedVariantText: {
    color: '#6366f1',
    fontWeight: 'bold',
  },
  noVariantsText: {
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 16,
  },
  pricingSection: {
    marginBottom: 8,
  },
  priceInfo: {
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  priceLabel: {
    color: '#6b7280',
    fontSize: 14,
  },
  basePrice: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  discountText: {
    color: '#10b981',
    fontSize: 14,
    fontWeight: '500',
  },
  sellingPrice: {
    color: '#6366f1',
    fontSize: 16,
    fontWeight: 'bold',
  },
  controlsSection: {
    marginBottom: 8,
  },
  inputRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  inputContainer: {
    flex: 1,
  },
  input: {
    backgroundColor: 'white',
  },
  finalPriceDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#eef2ff',
    padding: 12,
    borderRadius: 8,
  },
  finalPriceLabel: {
    color: '#6366f1',
    fontSize: 16,
    fontWeight: '500',
  },
  finalPriceValue: {
    color: '#6366f1',
    fontSize: 18,
    fontWeight: 'bold',
  },
  // Error styles
  errorCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: '#ef4444',
    marginLeft: 8,
    flex: 1,
  },
  // Global mode styles
  globalCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#6366f1',
  },
  globalIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eef2ff',
    padding: 8,
    borderRadius: 6,
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  globalText: {
    color: '#6366f1',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  variantCount: {
    color: '#6b7280',
    fontSize: 12,
    marginTop: 4,
  },
  variantsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  variantChip: {
    backgroundColor: '#f3f4f6',
  },
  variantChipText: {
    color: '#6b7280',
    fontSize: 12,
  },
  updatingInput: {
    backgroundColor: '#fef3c7',
  },
  globalPriceDisplay: {
    backgroundColor: '#eef2ff',
    borderColor: '#6366f1',
    borderWidth: 1,
  },
  priceDisplayLeft: {
    flex: 1,
  },
  globalNote: {
    color: '#6366f1',
    fontSize: 12,
    marginTop: 2,
  },
  globalPriceValue: {
    color: '#6366f1',
    fontSize: 20,
    fontWeight: 'bold',
  },
  globalWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
  },
  globalWarningText: {
    color: '#f59e0b',
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
});

export default EnhancedPricingCard;
