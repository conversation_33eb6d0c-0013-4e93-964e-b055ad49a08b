import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Share, StyleSheet, Animated, FlatList, Image, ActivityIndicator } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Breadcrumb from '../../components/common/Breadcrumb';
import ProductList from '../../components/ProductListing/ProductList';
import FilterModal from '../../components/ProductListing/FilterModal';
import SortModal from '../../components/ProductListing/SortModal';
import { Product, FilterOptions, ProductListingScreenProps } from '../../components/ProductListing/types';
import { useUser } from '../../context/UserContext';
import { UserRole } from '../../data/mockData';
import {useGetCategoriesQuery, useGetProductsByCategoryQuery, useGetProductsQuery } from '../../services/api/apiSlice';
import { colors } from '../../components/Home/CategoryTabs';
import CardSkeleton from '../../components/common/skeletons/CardSkeleton';

interface Category {
  id: number;
  name: string;
  urls: string[];
  description: string;
}

const ProductListingScreen: React.FC<ProductListingScreenProps> = ({ route, navigation }) => {
  const { category = 'All Products', categoryId = 0 } = route.params || {};
  console.log('plp Category ID:', categoryId);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid');
  const [refreshing, setRefreshing] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSortModal, setShowSortModal] = useState(false);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const { currentUser } = useUser();
  const slideAnimation = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);

  // RTK Query hook for fetching products
  const {
    data: products,
    isLoading: isProductsLoading,
    isFetching,
    refetch
  } = useGetProductsByCategoryQuery(categoryId !== 0 ? categoryId : undefined, {
    skip: categoryId === 0 // Skip this query when viewing all categories
  });

  console.table('pp' , products)

  const {
    data,
    isLoading,
    error
  } = useGetCategoriesQuery({ page: 0, count: 10, type: 1,  status: 1 }, {
    // Cache categories for 5 minutes
    pollingInterval: 0
  });

  const {
    data : allProductsData,
    isLoading: isAllProductsLoading,
    isFetching: isAllProductsFetching,
    refetch: refetchAllProducts
} = useGetProductsQuery({  page: 0,
  count: 100,
  type: 2},{
    skip: categoryId !== 0, // Skip this query when viewing specific category
    // Cache all products for 5 minutes
    pollingInterval: 0
  })
  
  const categories: Category[] = data || [];

  console.log('plp Categories:', categories);

  useEffect(() => {
    if (categoryId === 0 && allProductsData) {
      // For "All" category, use data from useGetCategoriesQuery
      const formattedProducts = allProductsData.map((category: {
        id: number; 
        name: string; 
        description: string; 
        urls: string[] 
      }) => ({
        id: category.id,
        name: category.name,
        description: category.description,
        image: category.urls?.[0] || 'https://via.placeholder.com/150',
        category: 'All Products',
        // categoryId: 2  // Use type 2 for All category
      }));
      setFilteredProducts(formattedProducts);
      console.log('All category products:', formattedProducts);
    } else if (products && categoryId !== 0) {
      // For specific categories, use products from useGetProductsByCategoryQuery
      let formattedProducts = products.map((product: { id: number; name: string; description: string; urls: string[] }) => ({
        id: product.id,
        name: product.name,
        description: product.description,
        image: product.urls?.[0]?.replace('abcd', '') || 'https://via.placeholder.com/150',
        category: category,
        categoryId: categoryId
      }));
      setFilteredProducts(formattedProducts);
      console.log('Category products:', formattedProducts);
    }
  }, [products, allProductsData, data, currentUser, categoryId]);

  // Category filter state
  const [categoryFilters, setCategoryFilters] = useState<Array<{
    id: number;
    name: string;
    selected: boolean;
    color: string;
    image: string;
  }>>([]);

  // Initialize category filters
  useEffect(() => {
    if (!categories) return;
    
    const filters = categories.map((category, index) => ({
      id: category.id,
      name: category.name,
      selected: category.id === categoryId,
      color: colors[index % colors.length],
      image: category.urls?.[0]?.replace('abcd', '') || 'https://via.placeholder.com/150'
    }));
  
    filters.unshift({
      id: 0,
      name: 'All',
      selected: categoryId === 0,
      color: colors[0],
      image: 'https://cdn-icons-png.flaticon.com/512/3405/3405802.png'
    });
  
    setCategoryFilters(filters);
  }, [categories, categoryId]);

  // Scroll to selected category when filters are updated
  useEffect(() => {
    if (categoryFilters.length > 0) {
      const selectedIndex = categoryFilters.findIndex(cat => cat.selected);
      if (selectedIndex !== -1) {
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index: selectedIndex,
            animated: true,
            viewPosition: 0.5 // Center the item
          });
        }, 300); // Small delay to ensure the list is rendered
      }
    }
  }, [categoryFilters]);

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    priceRange: [0, 10000],
    categories: [],
    sortBy: 'popularity',
    inStock: true
  });

  // Handle category selection
  const handleCategorySelect = async (selectedCategoryId: number) => {
    const selectedIndex = categoryFilters.findIndex(cat => cat.id === selectedCategoryId);
    
    setCategoryFilters(prev => {
      return prev.map(cat => ({
        ...cat,
        selected: cat.id === selectedCategoryId
      }));
    });
    
    // Scroll to the selected category
    if (selectedIndex !== -1) {
      flatListRef.current?.scrollToIndex({
        index: selectedIndex,
        animated: true,
        viewPosition: 0.5 // Center the item
      });
    }
    
    navigation.setParams({
      category: categories.find(cat => cat.id === selectedCategoryId)?.name || 'All Products',
      categoryId: selectedCategoryId
    });
    
    await refetch();
  };

  // Refresh handler
  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    refetch().finally(() => setRefreshing(false));
  }, [refetch]);

  // Share handler
  const handleShare = async (product: Product) => {
    try {
      await Share.share({
        message: `Check out ${product.name} at just ${product.price} for lot of ${product.quantity} units! \n\nProduct Details:\n${product.description}\n\nUnit Price: ${product.unitPrice}`,
      });
    } catch (error) {
      console.error('Error sharing product:', error);
    }
  };

  // Product press handler
  const handleProductPress = (product: Product) => {
    navigation.navigate('ProductDetail', { product: { id: `prod-${product.id}` } });
  };

  // Filter modal handlers
  const openFilterModal = () => {
    setShowFilterModal(true);
    Animated.timing(slideAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeFilterModal = () => {
    Animated.timing(slideAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => setShowFilterModal(false));
  };

  // Apply filters
  const applyFilters = async () => {
    try {
      await refetch();
    } catch (error) {
      console.error('Error applying filters:', error);
    } finally {
      closeFilterModal();
    }
  };

  // Reset filters
  const resetFilters = async () => {
    setFilterOptions({
      priceRange: [0, 10000],
      categories: [],
      sortBy: 'popularity',
      inStock: true
    });
    setSearchQuery('');
    await refetch();
    closeFilterModal();
  };

  // Render category filter item
  const renderCategoryFilter = ({ item, index }: { item: { id: number; name: string; selected: boolean; color: string; image: string }, index: number }) => (
    <TouchableOpacity
      style={[
        styles.categoryFilter,
        {
          backgroundColor: item.selected ? '#6366f1' : colors[(index) % colors.length]
        }
      ]}
      onPress={() => handleCategorySelect(item.id)}
    >
      <View style={styles.categoryImageContainer}>
      <Image
        source={{ uri: item.image }}
        style={styles.categoryImage}
        resizeMode="contain"
      />
    </View>
    <Text style={[
      styles.categoryText,
      item.selected && styles.selectedCategoryText
    ]}>
      {item.name}
    </Text>
    </TouchableOpacity>
  );

  const ProductListSkeleton = () => (
    <View style={{ padding: 16 }}>
      <FlatList
        data={[1, 2, 3, 4]}
        numColumns={2}
        renderItem={() => (
          <View style={{ flex: 1/2, padding: 8 }}>
            <CardSkeleton height={200} />
          </View>
        )}
      />
    </View>
  );

  return (
    <View className="flex-1 bg-gray-100">
      <Breadcrumb
        items={[
          { label: 'Home', screen: 'Home' },
          { label: category || 'All Products' }
        ]}
        showBackButton={false}
      />

      {/* Category Filters */}
      <View style={{ marginVertical: 8 }}>
        <FlatList
          ref={flatListRef}
          horizontal
          data={categoryFilters}
          renderItem={renderCategoryFilter}
          keyExtractor={item => item.id.toString()}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 8 }}
          getItemLayout={(data, index) => ({
            length: 132, // Approximate width of each item
            offset: 132 * index,
            index,
          })}
          onScrollToIndexFailed={info => {
            const wait = new Promise(resolve => setTimeout(resolve, 500));
            wait.then(() => {
              if (categoryFilters.length > 0) {
                const selectedIndex = categoryFilters.findIndex(cat => cat.selected);
                flatListRef.current?.scrollToIndex({ 
                  index: selectedIndex > 0 ? selectedIndex : 0, 
                  animated: true,
                  viewPosition: 0.5
                });
              }
            });
          }}
        />
      </View>

      {/* Header Controls */}
      {currentUser && currentUser.role !== UserRole.PUBLIC && (
        <View className="flex-row bg-white py-2 px-4 border-b border-gray-200 justify-between items-center">
          <TouchableOpacity
            className="flex-row items-center"
            onPress={openFilterModal}
          >
            <Icon name="filter-list" size={20} color="#6366f1" />
            <Text className="ml-1 text-gray-700 font-medium">Filter</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-row items-center"
            onPress={() => setShowSortModal(true)}
          >
            <Icon name="sort" size={20} color="#6366f1" />
            <Text className="ml-1 text-gray-700 font-medium">Sort</Text>
          </TouchableOpacity>

          <View className="flex-row">
            <TouchableOpacity
              onPress={() => setViewType('grid')}
              className={`p-1 mx-1 rounded ${viewType === 'grid' ? 'bg-indigo-100' : ''}`}
            >
              <Icon
                name="grid-view"
                size={20}
                color={viewType === 'grid' ? '#6366f1' : '#9ca3af'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setViewType('list')}
              className={`p-1 mx-1 rounded ${viewType === 'list' ? 'bg-indigo-100' : ''}`}
            >
              <Icon
                name="view-list"
                size={20}
                color={viewType === 'list' ? '#6366f1' : '#9ca3af'}
              />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Simple View Controls for Public Users */}
      {(!currentUser || currentUser.role === UserRole.PUBLIC) && (
        <View className="flex-row bg-white py-2 px-4 border-b border-gray-200 justify-end items-center">
          <View className="flex-row">
            <TouchableOpacity
              onPress={() => setViewType('grid')}
              className={`p-1 mx-1 rounded ${viewType === 'grid' ? 'bg-indigo-100' : ''}`}
            >
              <Icon
                name="grid-view"
                size={20}
                color={viewType === 'grid' ? '#6366f1' : '#9ca3af'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setViewType('list')}
              className={`p-1 mx-1 rounded ${viewType === 'list' ? 'bg-indigo-100' : ''}`}
            >
              <Icon
                name="view-list"
                size={20}
                color={viewType === 'list' ? '#6366f1' : '#9ca3af'}
              />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Product Count */}
      <View className="bg-gray-50 px-4 py-2">
        <Text className="text-gray-600 text-sm">
          {filteredProducts.length} products found
          {searchQuery.trim() !== '' && ` for "${searchQuery}"`}
        </Text>
      </View>

      {/* Product List */}
      {(isProductsLoading || isAllProductsLoading || isFetching || isAllProductsFetching) && !refreshing ? (
        <ProductListSkeleton />
      ) : (
        <ProductList
          viewType={viewType}
          products={filteredProducts}
          refreshing={refreshing}
          onRefresh={onRefresh}
          onProductPress={handleProductPress}
          onShare={handleShare}
        />
      )}

      {/* Filter Modal */}
      <FilterModal
        visible={showFilterModal}
        filterOptions={filterOptions}
        slideAnimation={slideAnimation}
        onClose={closeFilterModal}
        onApply={applyFilters}
        onReset={resetFilters}
        setFilterOptions={setFilterOptions}
        currentCategoryId={categoryId}
      />

      {/* Sort Modal */}
      <SortModal
        visible={showSortModal}
        currentSort={filterOptions.sortBy}
        onClose={() => setShowSortModal(false)}
        onSelect={(option) => {
          setFilterOptions(prev => ({ ...prev, sortBy: option }));
          setShowSortModal(false);
        }}
      />

      {/* FAB */}
      {currentUser && currentUser.role !== UserRole.PUBLIC && (
        <TouchableOpacity
          style={styles.fab}
          onPress={openFilterModal}
        >
          <Icon name="filter-alt" size={24} color="white" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  categoryFilter: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
    flexDirection: 'row',
    backgroundColor: 'transparent',
  },
  selectedCategoryFilter: {
    backgroundColor: '#6366f1',
  },
  categoryImageContainer: {
    backgroundColor: 'white',
    padding: 4,
    borderRadius: 12,
    marginRight: 6,
  },
  categoryImage: {
    width: 24,
    height: 24,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4b5563',
  },
  selectedCategoryText: {
    color: 'white',
  },
  fab: {
    position: 'absolute',
    width: 56,
    height: 56,
    alignItems: 'center',
    justifyContent: 'center',
    right: 20,
    bottom: 20,
    backgroundColor: '#6366f1',
    borderRadius: 28,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6b7280',
  }
});

export default ProductListingScreen;