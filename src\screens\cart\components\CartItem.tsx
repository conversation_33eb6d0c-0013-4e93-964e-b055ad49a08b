import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface CartItemProps {
  item: {
    id: number;
    name: string;
    price: string;
    image?: string;
    quantity: number;
    variant?: string;
    cartLineId?: number; // Added for server cart items
  };
  onRemove: (id: number, variant?: string , cartLineId?: number) => void;
  onUpdateQuantity: (id: number, quantity: number, variant?: string) => void;
  isLast?: boolean;
}

const CartItem = ({ item, onRemove, onUpdateQuantity, isLast }: CartItemProps) => {
  // Default image if none provided
  const imageUrl = item.image || 'https://via.placeholder.com/100';
  
  
  return (
    <View style={[styles.cartItem, isLast && styles.lastCartItem]}>
      <View style={styles.cartItemRow}>
        <Image
          source={{ uri: imageUrl }}
          style={styles.productImage}
          resizeMode="contain"
        />

        <View style={styles.productDetails}>
          <View style={styles.productHeader}>
            <View style={styles.productInfo}>
              <Text style={styles.productName} numberOfLines={2}>
                {item.name}
              </Text>
              {item.variant && (
                <View style={styles.variantContainer}>
                  <Text style={styles.variantText}>{item.variant}</Text>
                </View>
              )}
            </View>
            <TouchableOpacity
              onPress={() => onRemove(item.id, item.variant , item.cartLineId )}
              style={styles.removeButton}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Icon name="close" size={20} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.priceQuantityRow}>
            <Text style={styles.productPrice}>{item.price}</Text>
            <View style={styles.quantityControls}>
              <TouchableOpacity
                onPress={() => {
                  if (item.quantity > 1) {
                    onUpdateQuantity(item.id, item.quantity - 1, item.variant);
                  }
                }}
                style={[
                  styles.quantityButton,
                  item.quantity <= 1 && styles.quantityButtonDisabled
                ]}
                disabled={item.quantity <= 1}
              >
                <Icon
                  name="remove"
                  size={18}
                  color={item.quantity <= 1 ? "#d1d5db" : "#4b5563"}
                />
              </TouchableOpacity>
              <Text style={styles.quantityText}>{item.quantity}</Text>
              <TouchableOpacity
                onPress={() => onUpdateQuantity(item.id, item.quantity + 1, item.variant)}
                style={styles.quantityButton}
              >
                <Icon name="add" size={18} color="#4b5563" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cartItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 14,
    marginBottom: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  lastCartItem: {
    marginBottom: 0,
  },
  cartItemRow: {
    flexDirection: 'row',
  },
  productImage: {
    width: 90,
    height: 90,
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  productDetails: {
    flex: 1,
    marginLeft: 14,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  productInfo: {
    flex: 1,
    paddingRight: 10,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    lineHeight: 22,
  },
  variantContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  variantText: {
    fontSize: 12,
    color: '#6b7280',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  serverItemBadge: {
    marginTop: 4,
    backgroundColor: '#dbeafe',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  serverItemText: {
    fontSize: 10,
    color: '#2563eb',
    fontWeight: '500',
  },
  removeButton: {
    padding: 4,
  },
  priceQuantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1f2937',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 4,
  },
  quantityButton: {
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    backgroundColor: '#ffffff',
  },
  quantityButtonDisabled: {
    backgroundColor: '#f9fafb',
  },
  quantityText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4b5563',
    width: 30,
    textAlign: 'center',
  },
  itemTotalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  itemTotalLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  itemTotalValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4b5563',
  },
});

export default CartItem;