import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Image, 
  Dimensions, 
  FlatList, 
  Animated, 
  TouchableOpacity,
  Pressable,
  Text,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useGetBannersQuery } from './api/bannerslice';
import CardSkeleton from '../common/skeletons/CardSkeleton';

type RootStackParamList = {
  ProductListing: { 
    category: string;
    categoryId: string; 
  };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface BannerItem {
  id: string;
  imageUrl: string;
  link: string;
  name: string;
  categoryId: number;
}

interface ApiBanner {
  id: number | string;
  title: string;
  imageUrl: string;
  userId?: number;
  region?: string;
  state?: string;
  city?: string;
  area?: string;
  bannerType?: string;
  startDate?: string;
  endDate?: string;
  status?: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any; // For any other properties that might be in the response
}

// Fallback banner data in case API fails
const fallbackBannerData: BannerItem[] = [
  {
    id: '2',
    imageUrl: 'https://m.media-amazon.com/images/I/511T+P43JeL.jpg',
    link: 'ProductListing',
    name: 'TWS',
    categoryId: 2
  },
  {
    id: '4',
    imageUrl: 'https://www.simcard.sg/wp-content/uploads/Google-Banner.jpg',
    link: 'ProductListing',
    name: 'Earphone',
    categoryId: 4
  }
];

const Banner = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation<NavigationProp>();
  
  // Fetch banners from API
  const { data: apiResponse, isLoading, error } = useGetBannersQuery({
    status: 1,
    page: 0,
    size: 20
  });
  
  // Transform API data to match our BannerItem interface
  const bannerData = React.useMemo(() => {
    if (!apiResponse  || apiResponse.length === 0) {
      console.log('Using fallback banner data');
      return fallbackBannerData;
    }
    console.log('API banner data:', apiResponse);
    
    return apiResponse.map((banner: ApiBanner) => ({
      id: banner.id.toString(),
      imageUrl: banner.imageUrl.trim(),
      link: 'ProductListing',
      name: banner.title || 'Product',
      categoryId: banner.id
    }));
  }, [apiResponse]);
  
  const screenWidth = Dimensions.get('window').width;
  const ITEM_WIDTH = screenWidth; // 32 = mx-4 (16) * 2

  useEffect(() => {
    const timerId = setInterval(goToNextSlide, 5000);
    return () => clearInterval(timerId);
  }, [activeIndex, bannerData.length]);

  const goToNextSlide = () => {
    if (bannerData.length === 0) return;
    
    const nextIndex = (activeIndex + 1) % bannerData.length;
    flatListRef.current?.scrollToIndex({
      index: nextIndex,
      animated: true
    });
  };

  const handleBannerPress = (category: string, categoryId: any) => {
    navigation.navigate('ProductListing', { 
      category, 
      categoryId 
    });
  };

  const renderItem = ({ item }: { item: BannerItem }) => (
    <Pressable 
      onPress={() => handleBannerPress(item.name, item.categoryId)}
      className="py-2 px-2 overflow-hidden"
      style={{ width: ITEM_WIDTH }}
    >
      <Image
        source={{ uri: item.imageUrl }}
        style={{ height: 180, width: '100%',borderRadius: 10 }}
        resizeMode="cover"
      />
    </Pressable>
  );

  const renderDotIndicator = (index: number) => {
    const inputRange = [
      (index - 1) * ITEM_WIDTH,
      index * ITEM_WIDTH,
      (index + 1) * ITEM_WIDTH,
    ];

    const width = scrollX.interpolate({
      inputRange,
      outputRange: [6, 20, 6],
      extrapolate: 'clamp',
    });

    const opacity = scrollX.interpolate({
      inputRange,
      outputRange: [0.4, 1, 0.4],
      extrapolate: 'clamp',
    });

    return (
      <Animated.View
        key={index}
        className="h-2 rounded-full mx-1"
        style={{ 
          width, 
          opacity, 
          backgroundColor: index === activeIndex ? '#6366f1' : 'white' 
        }}
      />
    );
  };

  if (isLoading) {
    return (
      <View style={{ padding: 16 }}>
        <CardSkeleton height={150} />
      </View>
    );
  }

  if (error) {
    console.log('Banner error:', error);
  }

  return (
    <View className="mt-2">
      <FlatList
        ref={flatListRef}
        data={bannerData}
        renderItem={renderItem}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        onMomentumScrollEnd={(e) => {
          const newIndex = Math.round(
            e.nativeEvent.contentOffset.x / ITEM_WIDTH
          );
          setActiveIndex(newIndex);
        }}
        snapToInterval={ITEM_WIDTH}
        snapToAlignment="center"
        decelerationRate="fast"
        keyExtractor={(item) => item.id}
      />
      
      <View className="flex-row justify-center absolute bottom-3 left-0 right-0">
         {bannerData.map((_, index) => renderDotIndicator(index))}
      </View>
    </View>
  );
};

export default Banner;