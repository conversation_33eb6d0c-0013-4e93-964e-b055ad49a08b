import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity,  
  StyleSheet, 
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface OrderSummaryProps {
  subtotal: number;
  shippingCost?: number;
  discount?: number;
  total: number;
  items: Array<any>;
  onBuyNow: () => void;
  isLoading?: boolean;
}

const OrderSummary = ({ 
  subtotal, 
  shippingCost = 0,
  discount = 0,
  total,
  items,
  onBuyNow,
  isLoading = false
}: OrderSummaryProps) => {

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 2
    }).format(price);
  };

  const handleSubmit = () => {
      onBuyNow();
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Icon name="business" size={24} color="#4b5563" />
        <Text style={styles.headerTitle}>Request Product Quote</Text>
      </View>

      {items.length > 0 && (
        <View style={styles.productsContainer}>
          <Text style={styles.sectionTitle}>Products</Text>
          {items.map((item, index) => (
            <View key={`${item.id}-${item.variant || ''}-${index}`} style={styles.productItem}>
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productQuantity}>Qty: {item.quantity}</Text>
            </View>
          ))}
          <View style={styles.divider} />
          
          {/* Price Summary */}
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>Subtotal</Text>
            <Text style={styles.priceValue}>{formatPrice(subtotal)}</Text>
          </View>
          
          {shippingCost > 0 && (
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Shipping</Text>
              <Text style={styles.priceValue}>{formatPrice(shippingCost)}</Text>
            </View>
          )}
          
          {discount > 0 && (
            <View style={styles.priceRow}>
              <Text style={[styles.priceLabel, styles.discountLabel]}>Discount</Text>
              <Text style={[styles.priceValue, styles.discountValue]}>-{formatPrice(discount)}</Text>
            </View>
          )}
          
          <View style={[styles.priceRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>{formatPrice(total)}</Text>
          </View>
        </View>
      )}
      
      <TouchableOpacity
        style={[styles.buyButton, isLoading && styles.buyButtonDisabled]}
        onPress={handleSubmit}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color="#ffffff" />
        ) : (
          <>
            <Icon name="shopping-cart" size={20} color="#ffffff" />
            <Text style={styles.buyButtonText}>Buy Now</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginLeft: 8,
  },
  productsContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4b5563',
    marginBottom: 12,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  productName: {
    fontSize: 14,
    color: '#4b5563',
    flex: 1,
  },
  productQuantity: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  divider: {
    height: 1,
    backgroundColor: '#e5e7eb',
    marginVertical: 12,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  priceValue: {
    fontSize: 14,
    color: '#4b5563',
    fontWeight: '500',
  },
  discountLabel: {
    color: '#10b981',
  },
  discountValue: {
    color: '#10b981',
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1f2937',
  },
  buyButton: {
    backgroundColor: '#4f46e5',
    borderRadius: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buyButtonDisabled: {
    backgroundColor: '#a5b4fc',
  },
  buyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default OrderSummary;